#pragma once

#include <memory>

// LLVM includes for optimization passes
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/Module.h"
#include "llvm/IR/PassManager.h"
#include "llvm/Passes/PassBuilder.h"
#include "llvm/Passes/StandardInstrumentations.h"
#include "llvm/Transforms/InstCombine/InstCombine.h"
#include "llvm/Transforms/Scalar.h"
#include "llvm/Transforms/Scalar/GVN.h"
#include "llvm/Transforms/Scalar/Reassociate.h"
#include "llvm/Transforms/Scalar/SimplifyCFG.h"
#include "llvm/Transforms/Scalar/ADCE.h"
#include "llvm/Transforms/Scalar/EarlyCSE.h"
#include "llvm/Transforms/Utils/Mem2Reg.h"
#include "llvm/Transforms/IPO/GlobalDCE.h"
#include "llvm/Transforms/IPO/StripDeadPrototypes.h"
#include "llvm/Analysis/LoopAnalysisManager.h"
#include "llvm/Analysis/CGSCCPassManager.h"

/**
 * Optimizer - Responsible for LLVM IR optimization
 * This class manages LLVM optimization passes and provides
 * different optimization levels suitable for ARM targets.
 */
class Optimizer {
private:
    // LLVM pass and analysis managers
    std::unique_ptr<llvm::FunctionPassManager> function_pass_manager;
    std::unique_ptr<llvm::ModulePassManager> module_pass_manager;
    std::unique_ptr<llvm::LoopAnalysisManager> loop_analysis_manager;
    std::unique_ptr<llvm::FunctionAnalysisManager> function_analysis_manager;
    std::unique_ptr<llvm::CGSCCAnalysisManager> cgscc_analysis_manager;
    std::unique_ptr<llvm::ModuleAnalysisManager> module_analysis_manager;
    std::unique_ptr<llvm::PassInstrumentationCallbacks> pass_instrumentation_callbacks;
    std::unique_ptr<llvm::StandardInstrumentations> standard_instrumentations;

    // Target-specific optimization settings
    bool target_arm;
    int optimization_level; // 0=none, 1=basic, 2=aggressive, 3=maximum

    void setupBasicOptimizations();
    void setupAggressiveOptimizations();
    void setupMaximumOptimizations();
    void setupARMSpecificOptimizations();

public:
    enum OptimizationLevel {
        O0 = 0,  // No optimization
        O1 = 1,  // Basic optimization
        O2 = 2,  // Aggressive optimization
        O3 = 3   // Maximum optimization
    };

    Optimizer(OptimizationLevel level = O2, bool arm_target = true);
    ~Optimizer() = default;

    // Set optimization level
    void setOptimizationLevel(OptimizationLevel level);
    
    // Enable/disable ARM-specific optimizations
    void setARMTarget(bool enable) { target_arm = enable; }

    // Run optimization passes on the module
    void optimizeModule(llvm::Module* module);

    // Print optimization statistics
    void printOptimizationStats();
};
