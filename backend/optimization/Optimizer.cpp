#include "Optimizer.hpp"
#include <iostream>

Optimizer::Optimizer(OptimizationLevel level, bool arm_target) 
    : optimization_level(level), target_arm(arm_target) {
    
    // Initialize pass managers
    function_pass_manager = std::make_unique<llvm::FunctionPassManager>();
    module_pass_manager = std::make_unique<llvm::ModulePassManager>();
    loop_analysis_manager = std::make_unique<llvm::LoopAnalysisManager>();
    function_analysis_manager = std::make_unique<llvm::FunctionAnalysisManager>();
    cgscc_analysis_manager = std::make_unique<llvm::CGSCCAnalysisManager>();
    module_analysis_manager = std::make_unique<llvm::ModuleAnalysisManager>();
    pass_instrumentation_callbacks = std::make_unique<llvm::PassInstrumentationCallbacks>();
    standard_instrumentations = std::make_unique<llvm::StandardInstrumentations>();
    
    // Register standard instrumentations
    standard_instrumentations->registerCallbacks(*pass_instrumentation_callbacks);
    
    // Setup optimization passes based on level
    switch (level) {
        case O0:
            // No optimization
            break;
        case O1:
            setupBasicOptimizations();
            break;
        case O2:
            setupAggressiveOptimizations();
            break;
        case O3:
            setupMaximumOptimizations();
            break;
    }
    
    if (target_arm) {
        setupARMSpecificOptimizations();
    }
    
    std::cout << "Optimizer initialized with level O" << level 
              << (target_arm ? " (ARM target)" : "") << std::endl;
}

void Optimizer::setOptimizationLevel(OptimizationLevel level) {
    optimization_level = level;
    
    // Clear existing passes
    function_pass_manager = std::make_unique<llvm::FunctionPassManager>();
    module_pass_manager = std::make_unique<llvm::ModulePassManager>();
    
    // Re-setup passes
    switch (level) {
        case O0:
            break;
        case O1:
            setupBasicOptimizations();
            break;
        case O2:
            setupAggressiveOptimizations();
            break;
        case O3:
            setupMaximumOptimizations();
            break;
    }
    
    if (target_arm) {
        setupARMSpecificOptimizations();
    }
}

void Optimizer::setupBasicOptimizations() {
    std::cout << "Setting up basic optimizations (O1)..." << std::endl;

    // Basic function-level optimizations
    function_pass_manager->addPass(llvm::PromotePass()); // mem2reg - promote allocas to registers
    function_pass_manager->addPass(llvm::InstCombinePass()); // instruction combining
    function_pass_manager->addPass(llvm::SimplifyCFGPass()); // simplify control flow graph

    // Add basic dead code elimination (using ADCE - Aggressive Dead Code Elimination)
    function_pass_manager->addPass(llvm::ADCEPass()); // aggressive dead code elimination
}

void Optimizer::setupAggressiveOptimizations() {
    std::cout << "Setting up aggressive optimizations (O2)..." << std::endl;

    // Include basic optimizations
    setupBasicOptimizations();

    // Additional aggressive optimizations
    function_pass_manager->addPass(llvm::EarlyCSEPass()); // early common subexpression elimination
    function_pass_manager->addPass(llvm::SimplifyCFGPass()); // additional CFG simplification
    function_pass_manager->addPass(llvm::InstCombinePass()); // additional instruction combining

    // Add some module-level passes for interprocedural optimizations
    module_pass_manager->addPass(llvm::GlobalDCEPass()); // global dead code elimination
}

void Optimizer::setupMaximumOptimizations() {
    std::cout << "Setting up maximum optimizations (O3)..." << std::endl;

    // Include aggressive optimizations
    setupAggressiveOptimizations();

    // Additional maximum optimizations
    function_pass_manager->addPass(llvm::InstCombinePass()); // more instruction combining
    function_pass_manager->addPass(llvm::SimplifyCFGPass()); // more CFG simplification
    function_pass_manager->addPass(llvm::ADCEPass()); // additional dead code elimination

    // More aggressive module-level passes
    module_pass_manager->addPass(llvm::StripDeadPrototypesPass()); // remove unused function declarations
}

void Optimizer::setupARMSpecificOptimizations() {
    std::cout << "Setting up ARM-specific optimizations..." << std::endl;
    
    // ARM-specific optimization passes would be added here
    // These might include:
    // - ARM-specific instruction combining
    // - ARM addressing mode optimizations
    // - ARM-specific peephole optimizations
    
    // For now, we'll add some general passes that work well on ARM
    if (optimization_level >= O2) {
        // Additional passes that are beneficial for ARM
        function_pass_manager->addPass(llvm::InstCombinePass());
    }
}

void Optimizer::optimizeModule(llvm::Module* module) {
    if (!module) {
        std::cerr << "Error: Cannot optimize null module" << std::endl;
        return;
    }
    
    std::cout << "Running optimization passes on module: " << module->getName().str() << std::endl;
    
    // Register analysis managers
    llvm::PassBuilder pass_builder;
    pass_builder.registerModuleAnalyses(*module_analysis_manager);
    pass_builder.registerCGSCCAnalyses(*cgscc_analysis_manager);
    pass_builder.registerFunctionAnalyses(*function_analysis_manager);
    pass_builder.registerLoopAnalyses(*loop_analysis_manager);
    pass_builder.crossRegisterProxies(*loop_analysis_manager, *function_analysis_manager,
                                     *cgscc_analysis_manager, *module_analysis_manager);
    
    // Run function passes on each function
    for (auto& function : *module) {
        if (!function.isDeclaration()) {
            function_pass_manager->run(function, *function_analysis_manager);
        }
    }
    
    // Run module passes
    module_pass_manager->run(*module, *module_analysis_manager);
    
    std::cout << "Optimization passes completed." << std::endl;
}

void Optimizer::printOptimizationStats() {
    std::cout << "=== Optimization Statistics ===" << std::endl;
    std::cout << "Optimization Level: O" << optimization_level << std::endl;
    std::cout << "ARM Target: " << (target_arm ? "Yes" : "No") << std::endl;
    std::cout << "===============================" << std::endl;
}
