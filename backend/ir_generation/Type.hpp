#pragma once

#include "llvm/IR/Type.h"
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/DerivedTypes.h"
#include "SysY2022Parser.h"

/**
 * Type utilities for LLVM IR generation
 * These functions handle the mapping from SysY types to LLVM types
 */

// Convert SysY basic type to LLVM type
llvm::Type* get_llvm_type(SysY2022Parser::BTypeContext *ctx, llvm::LLVMContext& lctx);

// Convert SysY function type to LLVM function type
llvm::FunctionType* get_llvm_type(SysY2022Parser::FuncTypeContext *ctx, llvm::LLVMContext& lctx);
