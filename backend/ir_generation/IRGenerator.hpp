#pragma once

#include "antlr4-runtime.h"
#include "SysY2022BaseVisitor.h"
#include <iostream>
#include <memory>
#include <map>
#include <vector>

// LLVM includes
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/Module.h"
#include "llvm/IR/IRBuilder.h"
#include "llvm/IR/Value.h"
#include "llvm/IR/Function.h"
#include "llvm/IR/BasicBlock.h"
#include "llvm/IR/Type.h"
#include "llvm/IR/Verifier.h"
#include "llvm/Support/raw_ostream.h"

/**
 * IRGenerator - Responsible for converting AST to LLVM IR
 * This class handles the AST traversal and LLVM IR generation,
 * without any optimization or code generation concerns.
 *
 * The LLVM components (context, module, builder) are provided by the backend,
 * not owned by this class.
 */
class IRGenerator : public SysY2022BaseVisitor {
private:
    int indent = 0;

    // LLVM components for IR generation (NOT owned by this class)
    llvm::LLVMContext* llvm_context;
    llvm::Module* llvm_module;
    llvm::IRBuilder<>* llvm_builder;
    
    // Symbol table for variables
    std::map<std::string, std::vector<llvm::Value*>> named_values;
    std::map<std::string, llvm::Function*> function_table;
    
    llvm::Function* current_function = nullptr;
    llvm::Type* current_type = nullptr;
    llvm::ArrayType* current_array_type = nullptr;

    // Scope management
    std::vector<std::map<std::string, std::vector<llvm::Value*>>> scope_stack;

    // Loop context for break/continue statements
    struct LoopContext {
        llvm::BasicBlock* break_target;
        llvm::BasicBlock* continue_target;
        std::string loop_name;
    };
    std::vector<LoopContext> loop_stack;

    // Helper methods
    void printIndent();
    llvm::Constant* createZeroInitializedArray(llvm::Type* baseType, const std::vector<int>& dimensions);
    void pushScope();
    void popScope();
    void pushLoop(llvm::BasicBlock* break_target, llvm::BasicBlock* continue_target, const std::string& loop_name);
    void popLoop();
    llvm::BasicBlock* getCurrentBreakTarget();
    llvm::BasicBlock* getCurrentContinueTarget();
    llvm::Value* getLValAddress(SysY2022Parser::LValContext *ctx);

    // Helper methods for array initialization
    llvm::Constant* createPartiallyInitializedArray(llvm::Type* baseType, const std::vector<int>& dimensions, const std::vector<llvm::Constant*>& providedElements);
    llvm::Constant* createPartiallyInitialized2DArray(llvm::Type* baseType, const std::vector<int>& dimensions, const std::vector<llvm::Constant*>& providedElements);
    llvm::Constant* createPartiallyInitialized3DArray(llvm::Type* baseType, const std::vector<int>& dimensions, const std::vector<llvm::Constant*>& providedElements);
public:
    // Constructor takes LLVM components from the backend
    IRGenerator(llvm::LLVMContext* context, llvm::Module* module, llvm::IRBuilder<>* builder);
    ~IRGenerator() = default;

    // Initialize the runtime library (should be called after construction)
    void initializeRuntimeLibrary();
    
    // Print the generated LLVM IR
    void printLLVMIR();

    // AST visitor methods
    antlrcpp::Any visitCompUnit(SysY2022Parser::CompUnitContext *ctx) override;
    antlrcpp::Any visitProgram(SysY2022Parser::ProgramContext *ctx) override;
    antlrcpp::Any visitDecl(SysY2022Parser::DeclContext *ctx) override;
    antlrcpp::Any visitConstDecl(SysY2022Parser::ConstDeclContext *ctx) override;
    antlrcpp::Any visitBType(SysY2022Parser::BTypeContext *ctx) override;
    antlrcpp::Any visitConstDef(SysY2022Parser::ConstDefContext *ctx) override;
    antlrcpp::Any visitConstInitVal(SysY2022Parser::ConstInitValContext *ctx) override;
    antlrcpp::Any visitVarDecl(SysY2022Parser::VarDeclContext *ctx) override;
    antlrcpp::Any visitVarDef(SysY2022Parser::VarDefContext *ctx) override;
    antlrcpp::Any visitInitVal(SysY2022Parser::InitValContext *ctx) override;
    antlrcpp::Any visitFuncDef(SysY2022Parser::FuncDefContext *ctx) override;
    antlrcpp::Any visitFuncType(SysY2022Parser::FuncTypeContext *ctx) override;
    antlrcpp::Any visitFuncFormalParams(SysY2022Parser::FuncFormalParamsContext *ctx) override;
    antlrcpp::Any visitFuncFormalParam(SysY2022Parser::FuncFormalParamContext *ctx) override;
    antlrcpp::Any visitFuncRealParams(SysY2022Parser::FuncRealParamsContext *ctx) override;
    antlrcpp::Any visitBlock(SysY2022Parser::BlockContext *ctx) override;
    antlrcpp::Any visitBlockItem(SysY2022Parser::BlockItemContext *ctx) override;
    antlrcpp::Any visitStmt(SysY2022Parser::StmtContext *ctx) override;
    antlrcpp::Any visitExp(SysY2022Parser::ExpContext *ctx) override;
    antlrcpp::Any visitCond(SysY2022Parser::CondContext *ctx) override;
    antlrcpp::Any visitLVal(SysY2022Parser::LValContext *ctx) override;
    antlrcpp::Any visitPrimaryExp(SysY2022Parser::PrimaryExpContext *ctx) override;
    antlrcpp::Any visitNumber(SysY2022Parser::NumberContext *ctx) override;
    antlrcpp::Any visitUnaryExp(SysY2022Parser::UnaryExpContext *ctx) override;
    antlrcpp::Any visitUnaryOP(SysY2022Parser::UnaryOPContext *ctx) override;
    antlrcpp::Any visitMulExp(SysY2022Parser::MulExpContext *ctx) override;
    antlrcpp::Any visitAddExp(SysY2022Parser::AddExpContext *ctx) override;
    antlrcpp::Any visitRelExp(SysY2022Parser::RelExpContext *ctx) override;
    antlrcpp::Any visitEqExp(SysY2022Parser::EqExpContext *ctx) override;
    antlrcpp::Any visitLAndExp(SysY2022Parser::LAndExpContext *ctx) override;
    antlrcpp::Any visitLOrExp(SysY2022Parser::LOrExpContext *ctx) override;
    antlrcpp::Any visitConstExp(SysY2022Parser::ConstExpContext *ctx) override;
};
