#include "Backend.hpp"
#include <iostream>

Backend::Backend(const std::string& module_name, Optimizer::OptimizationLevel opt_level, bool arm_target)
    : module_name(module_name), opt_level(opt_level), arm_target(arm_target) {
    
    // Initialize LLVM components
    llvm_context = std::make_unique<llvm::LLVMContext>();
    llvm_module = std::make_unique<llvm::Module>(module_name, *llvm_context);
    llvm_builder = std::make_unique<llvm::IRBuilder<>>(*llvm_context);
    
    // Initialize backend components with LLVM components
    ir_generator = std::make_unique<IRGenerator>(llvm_context.get(), llvm_module.get(), llvm_builder.get());
    optimizer = std::make_unique<Optimizer>(opt_level, arm_target);
    code_generator = std::make_unique<ARMCodeGenerator>(ARMCodeGenerator::ARMv7, false, true);
    
    // Initialize runtime library
    ir_generator->initializeRuntimeLibrary();
    
    std::cout << "Backend initialized successfully:" << std::endl;
    std::cout << "  Module: " << module_name << std::endl;
    std::cout << "  Optimization Level: O" << static_cast<int>(opt_level) << std::endl;
    std::cout << "  ARM Target: " << (arm_target ? "Enabled" : "Disabled") << std::endl;
}

void Backend::generateIR(antlr4::tree::ParseTree* ast) {
    if (!ast) {
        std::cerr << "Error: Invalid AST provided to generateIR" << std::endl;
        return;
    }
    
    std::cout << "\n=== IR Generation Phase ===" << std::endl;
    ir_generator->visit(ast);
    std::cout << "IR generation completed successfully" << std::endl;
}

void Backend::optimizeIR() {
    std::cout << "\n=== Optimization Phase ===" << std::endl;
    optimizer->optimizeModule(llvm_module.get());
    std::cout << "IR optimization completed successfully" << std::endl;
}

void Backend::generateAssembly(const std::string& output_file) {
    std::cout << "\n=== Assembly Generation Phase ===" << std::endl;
    bool success = code_generator->generateAssembly(llvm_module.get(), output_file);
    if (success) {
        std::cout << "Assembly generation completed: " << output_file << std::endl;
    } else {
        std::cerr << "Error: Assembly generation failed" << std::endl;
    }
}

void Backend::generateObjectFile(const std::string& output_file) {
    std::cout << "\n=== Object File Generation Phase ===" << std::endl;
    bool success = code_generator->generateObjectFile(llvm_module.get(), output_file);
    if (success) {
        std::cout << "Object file generation completed: " << output_file << std::endl;
    } else {
        std::cerr << "Error: Object file generation failed" << std::endl;
    }
}

void Backend::generateExecutable(const std::string& output_file) {
    std::cout << "\n=== Executable Generation Phase ===" << std::endl;
    bool success = code_generator->generateExecutable(llvm_module.get(), output_file);
    if (success) {
        std::cout << "Executable generation completed: " << output_file << std::endl;
    } else {
        std::cerr << "Error: Executable generation failed" << std::endl;
    }
}

void Backend::compile(antlr4::tree::ParseTree* ast, const std::string& output_file, const std::string& format) {
    std::cout << "\n=== Starting Full Compilation Pipeline ===" << std::endl;
    
    // Phase 1: Generate LLVM IR from AST
    generateIR(ast);
    
    // Phase 2: Optimize LLVM IR
    optimizeIR();
    
    // Phase 3: Generate output based on format
    if (format == "assembly" || format == "asm" || format == "s") {
        generateAssembly(output_file);
    } else if (format == "object" || format == "obj" || format == "o") {
        generateObjectFile(output_file);
    } else if (format == "executable" || format == "exe") {
        generateExecutable(output_file);
    } else {
        std::cerr << "Error: Unknown output format '" << format << "'" << std::endl;
        std::cerr << "Supported formats: assembly, object, executable" << std::endl;
        return;
    }
    
    std::cout << "\n=== Compilation Pipeline Completed ===" << std::endl;
}

void Backend::printLLVMIR() {
    ir_generator->printLLVMIR();
}

void Backend::setOptimizationLevel(Optimizer::OptimizationLevel level) {
    opt_level = level;
    // Recreate optimizer with new level
    optimizer = std::make_unique<Optimizer>(opt_level, arm_target);
    std::cout << "Optimization level changed to O" << static_cast<int>(level) << std::endl;
}
