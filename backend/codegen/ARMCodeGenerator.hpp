#pragma once

#include <memory>
#include <string>
#include <vector>
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/Module.h"
#include "llvm/Support/FileSystem.h"
#include "llvm/Support/raw_ostream.h"
#include "llvm/IR/LegacyPassManager.h"

/**
 * ARMCodeGenerator - LLVM-based code generation module
 * This class uses LLVM's backend to generate ARM assembly and object files.
 * It leverages LLVM's target machine infrastructure for proper code generation.
 */
class ARMCodeGenerator {
public:
    enum ARMVersion {
        ARMv7 = 7,
        ARMv8 = 8
    };

private:
    ARMVersion arm_version;
    bool thumb_mode;
    bool hard_float;

    std::string target_triple;
    std::string target_cpu;
    std::string target_features;

    void setupARMTarget();
    bool writeToFile(llvm::Module* module, const std::string& output_file,
                     bool assembly_output = true);

public:
    // Constructor
    ARMCodeGenerator(ARMVersion version = ARMv7, bool thumb = false, bool hard_float = true);

    // Configuration methods
    void setARMVersion(ARMVersion version);
    void setThumbMode(bool enabled) { thumb_mode = enabled; }
    void setHardFloat(bool enabled) { hard_float = enabled; }

    // Code generation methods using LLVM backend
    bool generateAssembly(llvm::Module* module, const std::string& output_file);
    bool generateObjectFile(llvm::Module* module, const std::string& output_file);
    bool generateExecutable(llvm::Module* module, const std::string& output_file);

    // Utility methods
    void printTargetInfo();

    // Getters
    ARMVersion getARMVersion() const { return arm_version; }
    bool isThumbMode() const { return thumb_mode; }
    bool isHardFloat() const { return hard_float; }
    const std::string& getTargetTriple() const { return target_triple; }
    const std::string& getTargetCPU() const { return target_cpu; }
    const std::string& getTargetFeatures() const { return target_features; }
};
