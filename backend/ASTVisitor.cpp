#include "ASTVisitor.hpp"
#include "Type.hpp"
#include <iostream>
#include "llvm/IR/BasicBlock.h"
#include "llvm/IR/Constants.h"
#include "llvm/IR/Instructions.h"
#include "llvm/IR/Type.h"
#include "llvm/IR/Value.h"
#include "llvm/Support/Casting.h"
#include <vector>

ASTVisitor::ASTVisitor() {
    llvm_context = std::make_unique<llvm::LLVMContext>();
    llvm_module = std::make_unique<llvm::Module>("SysY2022", *llvm_context);
    llvm_builder = std::make_unique<llvm::IRBuilder<>>(*llvm_context);

    // Initialize SysY runtime library functions
    initializeRuntimeLibrary();
}

void ASTVisitor::print_llvm_ir() {
    llvm_module->print(llvm::outs(), nullptr);
}

// Run optimization passes including mem2reg
void ASTVisitor::optimize_ir() {
    // // Create a function pass manager
    // function_pass_manager = std::make_unique<llvm::FunctionPassManager>();

    // // Add transform passes.
    // // Promote allocas to registers.
    // function_pass_manager->addPass(llvm::PromotePass());
    // // Do simple "peephole" optimizations and bit-twiddling optzns.
    // function_pass_manager->addPass(llvm::InstCombinePass());
    // // Reassociate expressions.
    // function_pass_manager->addPass(llvm::ReassociatePass());
    // // Eliminate Common SubExpressions.
    // function_pass_manager->addPass(llvm::GVNPass());
    // // Simplify the control flow graph (deleting unreachable blocks, etc).
    // function_pass_manager->addPass(llvm::SimplifyCFGPass());

    // // // Add the mem2reg pass to promote allocas to registers
    // // fpm.add(llvm::PromotePass());

    // // fpm.doInitialization();

    // // // Run the passes on all functions
    // // for (auto &function : *llvm_module) {
    // //     fpm.run(function);
    // // }

    // // fpm.doFinalization();

    // // std::cout << "\n=== Mem2Reg optimization completed ===" << std::endl;
}

// Initialize SysY runtime library functions (external declarations only)
// Based on SysY 2022 official specification
void ASTVisitor::initializeRuntimeLibrary() {
    std::cout << "Declaring SysY 2022 runtime library functions..." << std::endl;

    // === SysY 2022 Official I/O Functions ===

    // 1) int getint() - 输入一个整数，返回对应的整数值
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getInt32Ty(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getint", llvm_module.get());
        function_table["getint"] = func;
    }

    // 2) int getch() - 输入一个字符，返回字符对应的 ASCII 码值
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getInt32Ty(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getch", llvm_module.get());
        function_table["getch"] = func;
    }

    // 3) float getfloat() - 输入一个浮点数，返回对应的浮点数值
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getFloatTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getfloat", llvm_module.get());
        function_table["getfloat"] = func;
    }

    // 4) int getarray(int[]) - 输入一串整数，第1个整数代表后续输入的整数个数，返回整数个数
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::PointerType::get(llvm::Type::getInt32Ty(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getInt32Ty(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getarray", llvm_module.get());
        function_table["getarray"] = func;
    }

    // 5) int getfarray(float[]) - 输入一个整数后再输入若干个浮点数，返回浮点数个数
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::PointerType::get(llvm::Type::getFloatTy(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getInt32Ty(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getfarray", llvm_module.get());
        function_table["getfarray"] = func;
    }

    // 6) void putint(int) - 输出一个整数的值
    {
        std::vector<llvm::Type*> paramTypes = {llvm::Type::getInt32Ty(*llvm_context)};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putint", llvm_module.get());
        function_table["putint"] = func;
    }

    // 7) void putch(int) - 将整数参数的值作为 ASCII 码，输出对应的字符
    {
        std::vector<llvm::Type*> paramTypes = {llvm::Type::getInt32Ty(*llvm_context)};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putch", llvm_module.get());
        function_table["putch"] = func;
    }

    // 8) void putfloat(float) - 输出一个浮点数的值
    {
        std::vector<llvm::Type*> paramTypes = {llvm::Type::getFloatTy(*llvm_context)};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putfloat", llvm_module.get());
        function_table["putfloat"] = func;
    }

    // 9) void putarray(int, int[]) - 输出整数数组
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::Type::getInt32Ty(*llvm_context),
            llvm::PointerType::get(llvm::Type::getInt32Ty(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putarray", llvm_module.get());
        function_table["putarray"] = func;
    }

    // 10) void putfarray(int, float[]) - 输出浮点数数组
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::Type::getInt32Ty(*llvm_context),
            llvm::PointerType::get(llvm::Type::getFloatTy(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putfarray", llvm_module.get());
        function_table["putfarray"] = func;
    }

    // 11) void putf(format, ...) - 格式化输出 (variadic function)
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::PointerType::get(llvm::Type::getInt8Ty(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, true); // variadic
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putf", llvm_module.get());
        function_table["putf"] = func;
    }

    // === Timing Functions ===

    // 12) void starttime() - 开始计时器
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "starttime", llvm_module.get());
        function_table["starttime"] = func;
    }

    // 13) void stoptime() - 停止计时器，输出计时结果
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "stoptime", llvm_module.get());
        function_table["stoptime"] = func;
    }

    std::cout << "SysY 2022 runtime library functions declared: " << function_table.size() << " functions" << std::endl;
}

antlrcpp::Any ASTVisitor::visitProgram(SysY2022Parser::ProgramContext *ctx) {
    printIndent();
    std::cout << "Program (" << std::endl;
    indent++;

    for (auto child : ctx->children) {
        visit(child);
    }
    
    indent--;
    printIndent();
    std::cout << ") End Program" << std::endl;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitCompUnit(SysY2022Parser::CompUnitContext *ctx) {
    printIndent();
    std::cout << "CompUnit (" << std::endl;
    indent++;
    
    for (auto child : ctx->children) {
        visit(child);
    }
    
    indent--;
    printIndent();
    std::cout << ") End CompUnit" << std::endl;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitDecl(SysY2022Parser::DeclContext *ctx) {
    printIndent();
    indent++;
    if (ctx->varDecl()) {
        std::cout << "Var Decl" << std::endl;
        visit(ctx->varDecl());
    } else if (ctx->constDecl()) {
        std::cout << "Const Decl" << std::endl;
        visit(ctx->constDecl());
    } else {
        std::cerr << "Unknown declaration: " << ctx->getText() << std::endl;
    }
    
    
    indent--;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitConstDecl(SysY2022Parser::ConstDeclContext *ctx) {
    printIndent();
    std::cout << "ConstDecl" << std::endl;
    indent++;
    // get type
    this->current_type = get_llvm_type(ctx->bType(), *llvm_context);
    // visit constDef
    for (auto const_def : ctx->constDef()) {
        visit(const_def);
    }
    this->current_type = nullptr;
    indent--;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitConstDef(SysY2022Parser::ConstDefContext *ctx) {
    printIndent();
    indent++;
    std::string const_name = ctx->IDENT()->getText();
    std::cout << "ConstDef: " << const_name << " (" << std::endl;

    // array dimensions
    std::vector<int> dimensions;
    if (!ctx->LBRACKET().empty()) {
        for (auto constExp : ctx->constExp()) {
            llvm::Value* sizeValue = visit(constExp);
            if (auto constInt = llvm::dyn_cast<llvm::ConstantInt>(sizeValue)) {
                dimensions.push_back(constInt->getZExtValue());
            } else {
                std::cerr << "Array size is not a constant integer" << std::endl;
                // fall back to 1 dimension in this layer
                dimensions.push_back(1);
            }
        }

        // create array type
        llvm::Type* arrayType = get_llvm_type((((SysY2022Parser::ConstDeclContext*)ctx->parent)->bType()), *llvm_context);
        for (int i = dimensions.size() - 1; i >= 0; i--) {
            arrayType = llvm::ArrayType::get(arrayType, dimensions[i]);
        }

        // Process array initialization
        if (ctx->constInitVal()) {
            std::cout << " = " << ctx->constInitVal()->getText() << std::endl;
            llvm::Value* initValue = visit(ctx->constInitVal());

            llvm::Constant* constArray = nullptr;
            if (initValue) {
                // Non-empty initialization
                constArray = llvm::dyn_cast<llvm::Constant>(initValue);
            } else {
                // Empty initialization: const int a[5] = {};
                std::cout << "Creating zero-initialized constant array" << std::endl;
                constArray = createZeroInitializedArray(current_type, dimensions);
            }

            if (constArray) {
                if (current_function) {
                    // Local constant array - use alloca
                    llvm::AllocaInst* alloca = llvm_builder->CreateAlloca(arrayType, nullptr, const_name);
                    llvm_builder->CreateStore(constArray, alloca);

                    std::vector<llvm::Value*> value_list = {alloca};
                    named_values[const_name] = value_list;
                } else {
                    // Global constant array
                    llvm::GlobalVariable* globalArray = new llvm::GlobalVariable(
                        *llvm_module, arrayType, true, // isConstant = true
                        llvm::GlobalValue::InternalLinkage, constArray, const_name);

                    std::vector<llvm::Value*> value_list = {globalArray};
                    named_values[const_name] = value_list;
                }
            }
        }
    } else {
        // Scalar constant
        if (ctx->constInitVal()) {
            std::cout << " = " << ctx->constInitVal()->getText() << std::endl;
            llvm::Value* initValue = visit(ctx->constInitVal());

            if (auto constValue = llvm::dyn_cast<llvm::Constant>(initValue)) {
                if (current_function) {
                    // Local scalar constant - store directly in symbol table
                    std::vector<llvm::Value*> value_list = {constValue};
                    named_values[const_name] = value_list;
                } else {
                    // Global scalar constant
                    llvm::GlobalVariable* globalConst = new llvm::GlobalVariable(
                        *llvm_module, current_type, true, // isConstant = true
                        llvm::GlobalValue::InternalLinkage, constValue, const_name);

                    std::vector<llvm::Value*> value_list = {globalConst};
                    named_values[const_name] = value_list;
                }
            }
        } else {
            std::cout << std::endl;
            std::cerr << "Constant must have initialization value" << std::endl;
        }
    }
    indent--;
    printIndent();
    std::cout << ") End ConstDef for " << const_name;
    for (int i = 0; i < dimensions.size(); i++) {
        std::cout << "[" << dimensions[i] << "]";
    }
    std::cout << std::endl;
    return nullptr;
}

// visit constInitVal
antlrcpp::Any ASTVisitor::visitConstInitVal(SysY2022Parser::ConstInitValContext *ctx) {
    printIndent();
    indent++;
    llvm::Value *llvm_constInitVal = nullptr;

    if (ctx->constExp()) {
        // Single value: constExp
        llvm_constInitVal = visit(ctx->constExp());
        std::cout << "ConstInitVal (Single): " << ctx->constExp()->getText() << std::endl;

    } else if (ctx->LBRACE()) {
        // Array initialization: { constInitVal, constInitVal, ... }
        std::cout << "ConstInitVal (Array): {";

        std::vector<llvm::Constant*> elements;

        if (!ctx->constInitVal().empty()) {
            // Non-empty array: {1, 2, 3}
            for (size_t i = 0; i < ctx->constInitVal().size(); i++) {
                if (i > 0) std::cout << ", ";

                llvm::Value* element = visit(ctx->constInitVal(i));
                if (auto constElement = llvm::dyn_cast<llvm::Constant>(element)) {
                    elements.push_back(constElement);
                } else {
                    std::cerr << "Array element must be constant" << std::endl;
                    // Create a zero constant as fallback based on current_type
                    if (current_type->isIntegerTy()) {
                        elements.push_back(llvm::ConstantInt::get(current_type, 0));
                    } else if (current_type->isFloatTy()) {
                        elements.push_back(llvm::ConstantFP::get(current_type, 0.0));
                    } else {
                        elements.push_back(llvm::Constant::getNullValue(current_type));
                    }
                }
            }
        }
        // else: empty array initialization {}

        std::cout << "}" << std::endl;

        // Create array constant
        if (!elements.empty()) {
            llvm::ArrayType* arrayType = llvm::ArrayType::get(current_type, elements.size());
            llvm_constInitVal = llvm::ConstantArray::get(arrayType, elements);
        } else {
            // Empty initialization: const int a[5] = {};
            // We'll return nullptr and let the caller (visitConstDef) handle it
            // since it has access to the array dimensions
            std::cout << "Empty constant array initialization detected" << std::endl;
            llvm_constInitVal = nullptr;
        }

    } else {
        std::cerr << "Unknown constInitVal: " << ctx->getText() << std::endl;
    }

    indent--;
    return llvm_constInitVal;
}

// visit constExp
antlrcpp::Any ASTVisitor::visitConstExp(SysY2022Parser::ConstExpContext *ctx) {
    printIndent();
    indent++;
    llvm::Value* llvm_constExp = nullptr;
    if (ctx->addExp()) {
        llvm_constExp = visit(ctx->addExp());
        std::cout << "ConstExp: " << ctx->addExp()->getText() << std::endl;
    } else {
        std::cerr << "Unknown constExp: " << ctx->getText() << std::endl;
    }
    indent--;
    return llvm_constExp;
}

// visit bType
// bType: INT | FLOAT;
antlrcpp::Any ASTVisitor::visitBType(SysY2022Parser::BTypeContext *ctx) {
    printIndent();
    std::cout << "BType: " << ctx->getText() << std::endl;

    if (ctx->INT()) {
        return llvm::Type::getInt32Ty(*llvm_context);
    } else if (ctx->FLOAT()) {
        return llvm::Type::getFloatTy(*llvm_context);
    }

    return llvm::Type::getInt32Ty(*llvm_context); // fallback
}

// visit varDef
// varDef
//     : IDENT ( LBRACKET constExp RBRACKET )*
//     | IDENT ( LBRACKET constExp RBRACKET )* ASSIGN initVal;
antlrcpp::Any ASTVisitor::visitVarDef(SysY2022Parser::VarDefContext *ctx) {
    printIndent();
    std::cout << "VarDef: " << ctx->IDENT()->getText() << std::endl;

    // This is handled in visitVarDecl, so just return the identifier
    return ctx->IDENT()->getText();
}

// visit blockItem
// blockItem
//     : decl | stmt;
antlrcpp::Any ASTVisitor::visitBlockItem(SysY2022Parser::BlockItemContext *ctx) {
    printIndent();
    std::cout << "BlockItem (" << std::endl;
    indent++;

    if (ctx->decl()) {
        visit(ctx->decl());
    } else if (ctx->stmt()) {
        visit(ctx->stmt());
    }

    indent--;
    printIndent();
    std::cout << ") End BlockItem" << std::endl;
    return nullptr;
}

// visit cond
// cond: lOrExp;
antlrcpp::Any ASTVisitor::visitCond(SysY2022Parser::CondContext *ctx) {
    printIndent();
    std::cout << "Cond (" << std::endl;
    indent++;

    llvm::Value* condValue = visit(ctx->lOrExp());

    indent--;
    printIndent();
    std::cout << ") End Cond" << std::endl;

    return condValue;
}

// visit unaryOP
// unaryOP
//     : ADD | SUB | NOT;
antlrcpp::Any ASTVisitor::visitUnaryOP(SysY2022Parser::UnaryOPContext *ctx) {
    printIndent();
    std::cout << "UnaryOP: ";

    if (ctx->ADD()) {
        std::cout << "+" << std::endl;
        return std::string("+");
    } else if (ctx->SUB()) {
        std::cout << "-" << std::endl;
        return std::string("-");
    } else if (ctx->NOT()) {
        std::cout << "!" << std::endl;
        return std::string("!");
    }

    return std::string(""); // fallback
}


// funcDef
//     : funcType IDENT LPAREN ( funcFormalParams )? RPAREN block;
antlrcpp::Any ASTVisitor::visitFuncDef(SysY2022Parser::FuncDefContext *ctx) {
    std::string func_name = ctx->IDENT()->getText();

    printIndent();
    std::cout << "FuncDef: " << func_name << " (" << std::endl;
    indent++;

    printIndent();
    std::cout << "FuncResultType: " << ctx->funcType()->getText() << std::endl;

    printIndent();
    std::cout << "FuncFormalParams: "
        << (ctx->funcFormalParams() ? ctx->funcFormalParams()->getText() : "(void)") << std::endl;
    
    // Get return type
    llvm::Type* returnType = visit(ctx->funcType());

    // Collect parameter types
    std::vector<llvm::Type*> paramTypes;
    std::vector<std::string> paramNames;

    if (ctx->funcFormalParams()) {
        // Visit parameters to collect types and names
        auto paramInfo = visit(ctx->funcFormalParams());
        // paramInfo will be a pair of vectors: <types, names>
        try {
            auto params = paramInfo.as<std::pair<std::vector<llvm::Type*>, std::vector<std::string>>>();
            paramTypes = params.first;
            paramNames = params.second;
        } catch (...) {
            std::cerr << "Error: Failed to get parameter information" << std::endl;
        }
    }

    // Create function type
    llvm::FunctionType *funcType = llvm::FunctionType::get(returnType, paramTypes, false);

    // Create function
    llvm::Function *function = llvm::Function::Create(
        funcType, llvm::Function::ExternalLinkage,
        func_name, llvm_module.get());

    function_table[func_name] = function;
    current_function = function;

    // Set parameter names
    auto argIt = function->arg_begin();
    for (size_t i = 0; i < paramNames.size(); ++i, ++argIt) {
        argIt->setName(paramNames[i]);
    }


    // Create basic block
    llvm::BasicBlock *bb = llvm::BasicBlock::Create( *llvm_context, "entry", function);
    llvm_builder->SetInsertPoint(bb);

    // Create allocas for parameters (to support assignment to parameters)
    argIt = function->arg_begin();
    for (size_t i = 0; i < paramNames.size(); ++i, ++argIt) {
        llvm::AllocaInst* paramAlloca = llvm_builder->CreateAlloca(
            paramTypes[i], nullptr, paramNames[i]);
        llvm_builder->CreateStore(&*argIt, paramAlloca);

        // Add to symbol table
        std::vector<llvm::Value*> value_list = {paramAlloca};
        named_values[paramNames[i]] = value_list;

        printIndent();
        std::cout << "Parameter allocated: " << paramNames[i] << std::endl;
    }

    // Visit function body
    visit(ctx->block());

    // Add default return if function doesn't end with return
    if (!llvm_builder->GetInsertBlock()->getTerminator()) {
        if (returnType->isVoidTy()) {
            llvm_builder->CreateRetVoid();
        } else {
            // Return zero for non-void functions without explicit return
            if (returnType->isIntegerTy()) {
                llvm_builder->CreateRet(llvm::ConstantInt::get(returnType, 0));
            } else if (returnType->isFloatTy()) {
                llvm_builder->CreateRet(llvm::ConstantFP::get(returnType, 0.0));
            } else {
                llvm_builder->CreateRet(llvm::Constant::getNullValue(returnType));
            }
        }
    }

    // Reset current function
    current_function = nullptr;
    
    indent--;
    printIndent();
    std::cout << ") End FuncDef" << std::endl;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitBlock(SysY2022Parser::BlockContext *ctx) {
    printIndent();
    std::cout << "Block (" << std::endl;
    indent++;

    // Push new scope for this block
    pushScope();

    // Get current basic block for control flow
    llvm::BasicBlock* llvm_block = nullptr;
    if (current_function && llvm_builder->GetInsertBlock()) {
        llvm_block = llvm_builder->GetInsertBlock();
        printIndent();
        std::cout << "Using BasicBlock: " << llvm_block->getName().str() << std::endl;
    }

    // Process all block items (declarations and statements)
    for (auto item : ctx->blockItem()) {
        visit(item);
    }

    // Pop scope to restore previous symbol table state
    // This removes variables declared in this block
    popScope();

    indent--;
    printIndent();
    std::cout << ") End Block" << std::endl;

    return llvm_block;
}

// visit funcType
antlrcpp::Any ASTVisitor::visitFuncType(SysY2022Parser::FuncTypeContext *ctx) {
    printIndent();
    std::cout << "FuncType: " << ctx->getText() << std::endl;

    if (ctx->VOID()) {
        return llvm::Type::getVoidTy(*llvm_context);
    } else if (ctx->bType()) {
        return get_llvm_type(ctx->bType(), *llvm_context);
    }

    return llvm::Type::getVoidTy(*llvm_context); // fallback
}

// visit funcFormalParams
antlrcpp::Any ASTVisitor::visitFuncFormalParams(SysY2022Parser::FuncFormalParamsContext *ctx) {
    printIndent();
    std::cout << "FuncFormalParams (" << std::endl;
    indent++;

    std::vector<llvm::Type*> paramTypes;
    std::vector<std::string> paramNames;

    for (auto param : ctx->funcFormalParam()) {
        auto paramInfo = visit(param);
        try {
            auto info = paramInfo.as<std::pair<llvm::Type*, std::string>>();
            paramTypes.push_back(info.first);
            paramNames.push_back(info.second);
        } catch (...) {
            std::cerr << "Error: Failed to get parameter info" << std::endl;
        }
    }

    indent--;
    printIndent();
    std::cout << ") End FuncFormalParams" << std::endl;

    return std::make_pair(paramTypes, paramNames);
}

// visit funcFormalParam
antlrcpp::Any ASTVisitor::visitFuncFormalParam(SysY2022Parser::FuncFormalParamContext *ctx) {
    printIndent();
    std::cout << "FuncFormalParam: ";

    std::string paramName = ctx->IDENT()->getText();
    llvm::Type* paramType = get_llvm_type(ctx->bType(), *llvm_context);

    // Check if it's an array parameter
    if (!ctx->LBRACKET().empty()) {
        std::cout << paramName << "[] (Array parameter)" << std::endl;
        // For array parameters, we use pointer type
        paramType = llvm::PointerType::get(paramType, 0);
    } else {
        std::cout << paramName << " (Scalar parameter)" << std::endl;
    }

    return std::make_pair(paramType, paramName);
}

// visit funcRealParams
antlrcpp::Any ASTVisitor::visitFuncRealParams(SysY2022Parser::FuncRealParamsContext *ctx) {
    printIndent();
    std::cout << "FuncRealParams (" << std::endl;
    indent++;

    auto args = std::make_shared<std::vector<llvm::Value*>>();

    for (auto exp : ctx->exp()) {
        llvm::Value* arg = visit(exp);
        if (arg) {
            args->push_back(arg);
        }
    }

    indent--;
    printIndent();
    std::cout << ") End FuncRealParams" << std::endl;

    return args;
}

// Helper function to create zero-initialized array constant
llvm::Constant* ASTVisitor::createZeroInitializedArray(llvm::Type* baseType, const std::vector<int>& dimensions) {
    if (dimensions.empty()) {
        // Scalar zero value
        if (baseType->isIntegerTy()) {
            return llvm::ConstantInt::get(baseType, 0);
        } else if (baseType->isFloatTy()) {
            return llvm::ConstantFP::get(baseType, 0.0);
        } else {
            return llvm::Constant::getNullValue(baseType);
        }
    }

    // Create array type
    llvm::Type* arrayType = baseType;
    for (int i = dimensions.size() - 1; i >= 0; i--) {
        arrayType = llvm::ArrayType::get(arrayType, dimensions[i]);
    }

    // Create zero-initialized array constant
    return llvm::Constant::getNullValue(arrayType);
}

// Scope management functions
void ASTVisitor::pushScope() {
    // Save current symbol table state
    scope_stack.push_back(named_values);
}

void ASTVisitor::popScope() {
    if (!scope_stack.empty()) {
        // Restore previous symbol table state
        named_values = scope_stack.back();
        scope_stack.pop_back();
    }
}

// Loop context management functions
void ASTVisitor::pushLoop(llvm::BasicBlock* break_target, llvm::BasicBlock* continue_target, const std::string& loop_name) {
    LoopContext ctx;
    ctx.break_target = break_target;
    ctx.continue_target = continue_target;
    ctx.loop_name = loop_name;
    loop_stack.push_back(ctx);

    printIndent();
    std::cout << "Pushed loop context: " << loop_name << std::endl;
}

void ASTVisitor::popLoop() {
    if (!loop_stack.empty()) {
        printIndent();
        std::cout << "Popped loop context: " << loop_stack.back().loop_name << std::endl;
        loop_stack.pop_back();
    }
}

llvm::BasicBlock* ASTVisitor::getCurrentBreakTarget() {
    if (!loop_stack.empty()) {
        return loop_stack.back().break_target;
    }
    return nullptr;
}

llvm::BasicBlock* ASTVisitor::getCurrentContinueTarget() {
    if (!loop_stack.empty()) {
        return loop_stack.back().continue_target;
    }
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitVarDecl(SysY2022Parser::VarDeclContext *ctx) {
    printIndent();
    std::cout << "VarDecl (" << std::endl;
    indent++;

    llvm::Type* baseType = get_llvm_type(ctx->bType(), *llvm_context);

    // Process each variable definition
    for (auto var_def : ctx->varDef()) {
        std::string var_name = var_def->IDENT()->getText();
        printIndent();
        std::cout << "Variable: " << var_name;

        // Check if it's an array variable
        if (!var_def->constExp().empty()) {
            std::cout << " (Array)" << std::endl;

            // Calculate array dimensions
            std::vector<int> dimensions;
            for (auto constExp : var_def->constExp()) {
                printIndent();
                std::cout << "  Array dimension: ";
                llvm::Value* sizeValue = visit(constExp);

                if (auto constInt = llvm::dyn_cast<llvm::ConstantInt>(sizeValue)) {
                    int dimension = constInt->getSExtValue();
                    dimensions.push_back(dimension);
                    std::cout << "[" << dimension << "]" << std::endl;
                } else {
                    std::cerr << "Array size must be a constant integer" << std::endl;
                    dimensions.push_back(1); // fallback
                }
            }

            // Create array type
            llvm::Type* arrayType = baseType;
            for (int i = dimensions.size() - 1; i >= 0; i--) {
                arrayType = llvm::ArrayType::get(arrayType, dimensions[i]);
            }

            // Create alloca for array
            llvm::AllocaInst *alloca = llvm_builder->CreateAlloca(arrayType, nullptr, var_name);
            std::vector<llvm::Value*> value_list = {alloca};
            named_values[var_name] = value_list;

            // Handle initialization if present
            if (var_def->initVal()) {
                printIndent();
                std::cout << "  Array initialization: " << var_def->initVal()->getText() << std::endl;
                llvm::Value* initValue = visit(var_def->initVal());

                if (initValue) {
                    // Non-empty initialization
                    if (auto constValue = llvm::dyn_cast<llvm::Constant>(initValue)) {
                        llvm_builder->CreateStore(constValue, alloca);
                    }
                } else {
                    // Empty initialization: int a[5] = {};
                    printIndent();
                    std::cout << "  Creating zero-initialized array" << std::endl;
                    llvm::Constant* zeroArray = createZeroInitializedArray(baseType, dimensions);
                    llvm_builder->CreateStore(zeroArray, alloca);
                }
            }

        } else {
            // Scalar variable
            std::cout << " (Scalar)" << std::endl;

            // Create alloca for scalar
            llvm::AllocaInst *alloca = llvm_builder->CreateAlloca(baseType, nullptr, var_name);
            std::vector<llvm::Value*> value_list = {alloca};
            named_values[var_name] = value_list;

            // Handle initialization if present
            if (var_def->initVal()) {
                printIndent();
                std::cout << "  Initialization: " << var_def->initVal()->getText() << std::endl;
                llvm::Value* initValue = visit(var_def->initVal());

                if (initValue) {
                    // Non-empty initialization
                    llvm_builder->CreateStore(initValue, alloca);
                } else {
                    // Empty initialization: int a = {}; (though this is unusual for scalars)
                    printIndent();
                    std::cout << "  Creating zero-initialized scalar" << std::endl;
                    llvm::Constant* zeroValue = createZeroInitializedArray(baseType, {});
                    llvm_builder->CreateStore(zeroValue, alloca);
                }
            }
        }
    }

    indent--;
    printIndent();
    std::cout << ") End VarDecl" << std::endl;
    return nullptr;
}

// visit initVal
antlrcpp::Any ASTVisitor::visitInitVal(SysY2022Parser::InitValContext *ctx) {
    printIndent();
    indent++;
    llvm::Value *llvm_initVal = nullptr;

    if (ctx->exp()) {
        // Single value initialization: int a = 10;
        llvm_initVal = visit(ctx->exp());
        std::cout << "InitVal (Single): " << ctx->exp()->getText() << std::endl;

    } else if (ctx->LBRACE()) {
        // Array initialization: int a[2][3] = {{1,2,3}, {4,5,6}};
        std::cout << "InitVal (Array): {";

        std::vector<llvm::Value*> elements;

        if (!ctx->initVal().empty()) {
            // Non-empty array: {1, 2, 3} or {{1,2}, {3,4}}
            for (size_t i = 0; i < ctx->initVal().size(); i++) {
                if (i > 0) std::cout << ", ";

                llvm::Value* element = visit(ctx->initVal(i));
                if (element) {
                    elements.push_back(element);
                }
            }
        }
        // else: empty array initialization {}

        std::cout << "}" << std::endl;

        // For array initialization, we need to create appropriate structure
        if (!elements.empty()) {
            // Non-empty initialization: create array constant
            // TODO:
            // Note: This is a simplified approach - full implementation would need
            // to handle multi-dimensional arrays and proper type checking
            llvm_initVal = elements[0]; // Return first element as placeholder
        } else {
            // Empty initialization: {}
            // We'll return a special marker that the caller can detect
            // The caller (visitVarDecl) will handle creating the zero-initialized array
            std::cout << "Empty array initialization detected" << std::endl;
            llvm_initVal = nullptr; // Signal empty initialization
        }

    } else {
        std::cerr << "Unknown initVal: " << ctx->getText() << std::endl;
    }

    indent--;
    return llvm_initVal;
}

/*
stmt
    : lVal ASSIGN exp SEMICOLON
    | exp SEMICOLON
    | block
    | IF LPAREN cond RPAREN stmt ( ELSE stmt )?
    | WHILE LPAREN cond RPAREN stmt
    | BREAK SEMICOLON
    | CONTINUE SEMICOLON
    | RETURN ( exp )? SEMICOLON;
*/
antlrcpp::Any ASTVisitor::visitStmt(SysY2022Parser::StmtContext *ctx) {
    llvm::Instruction* llvm_stmt = nullptr;
    // Assignment statement: left_value = right_exp
    if (ctx->ASSIGN()) {
        // lVal ASSIGN exp SEMICOLON
        printIndent();
        std::cout << "Assignment Stmt (" << std::endl;
        indent++;
        if (!(ctx->lVal() && ctx->exp())) {
            std::cerr << "Assignment statement: left_value = right_exp" << std::endl;
            std::cerr << "lVal or exp is nullptr" << std::endl;
            exit(1);
        }
        // Get the address (alloca) for the left-hand side
        std::string var_name = ctx->lVal()->IDENT()->getText();
        llvm::Value* var_address = nullptr;

        if (named_values.find(var_name) != named_values.end()) {
            var_address = named_values[var_name][0];  // Get the alloca directly
        } else {
            std::cerr << "Undefined variable in assignment: " << var_name << std::endl;
        }

        // Get the value for the right-hand side
        llvm::Value* r_exp = visit(ctx->exp());

        if (!var_address || !r_exp) {
            std::cerr << "Assignment statement: left_value = right_exp" << std::endl;
            std::cerr << "var_address or r_exp is nullptr" << std::endl;
        } else {
            // Correct order: CreateStore(value, address)
            llvm_stmt = llvm_builder->CreateStore(r_exp, var_address);
            printIndent();
            std::cout << "Storing value to variable: " << var_name << std::endl;
        }
        indent--;
        printIndent();
        std::cout << ") End Assignment" << std::endl;
    } else if (ctx->RETURN()) {
        // Return statement
        // RETURN ( exp )? SEMICOLON;
        printIndent();
        std::cout << "Return Stmt (" << std::endl;
        indent++;
        
        if (ctx->exp()) {
            llvm::Value *retVal = visit(ctx->exp());
            llvm_stmt = llvm_builder->CreateRet(retVal);
        } else {
            llvm_stmt = llvm_builder->CreateRetVoid();
        }
        indent--;
        printIndent();
        std::cout << ") End Return" << std::endl;
    } else if (ctx->IF()) {
        // IF (ELSE) statement
        // IF LPAREN cond RPAREN stmt ( ELSE stmt )?
        printIndent();
        std::cout << "If Stmt (" << std::endl;
        indent++;

        // Evaluate condition first
        llvm::Value* cond = visit(ctx->cond());

        // Create basic blocks for control flow
        llvm::BasicBlock* then_block = llvm::BasicBlock::Create(*llvm_context, "if.then", current_function);
        llvm::BasicBlock* else_block = nullptr;
        llvm::BasicBlock* merge_block = llvm::BasicBlock::Create(*llvm_context, "if.end", current_function);

        if (ctx->ELSE()) {
            else_block = llvm::BasicBlock::Create(*llvm_context, "if.else", current_function);
        }

        // Create conditional branch
        if (else_block) {
            llvm_stmt = llvm_builder->CreateCondBr(cond, then_block, else_block);
        } else {
            llvm_stmt = llvm_builder->CreateCondBr(cond, then_block, merge_block);
        }

        // Generate then block
        llvm_builder->SetInsertPoint(then_block);
        visit(ctx->stmt(0));
        if (!llvm_builder->GetInsertBlock()->getTerminator()) {
            llvm_builder->CreateBr(merge_block);
        }

        // Generate else block if present
        if (ctx->ELSE()) {
            llvm_builder->SetInsertPoint(else_block);
            visit(ctx->stmt(1));
            if (!llvm_builder->GetInsertBlock()->getTerminator()) {
                llvm_builder->CreateBr(merge_block);
            }
        }

        // Continue with merge block
        llvm_builder->SetInsertPoint(merge_block);

        indent--;
        printIndent();
        std::cout << ") End If" << std::endl;
    } else if (ctx->WHILE()) {
        // WHILE statement
        // WHILE LPAREN cond RPAREN stmt
        printIndent();
        std::cout << "While Stmt (" << std::endl;
        indent++;

        // Create basic blocks for while loop
        llvm::BasicBlock* cond_block = llvm::BasicBlock::Create(*llvm_context, "while.cond", current_function);
        llvm::BasicBlock* body_block = llvm::BasicBlock::Create(*llvm_context, "while.body", current_function);
        llvm::BasicBlock* end_block = llvm::BasicBlock::Create(*llvm_context, "while.end", current_function);

        // Set up loop context for break/continue
        // break -> end_block, continue -> cond_block (re-evaluate condition)
        pushLoop(end_block, cond_block, "while");

        // Jump to condition block
        llvm_builder->CreateBr(cond_block);

        // Generate condition block
        llvm_builder->SetInsertPoint(cond_block);
        llvm::Value* cond = visit(ctx->cond());
        llvm_builder->CreateCondBr(cond, body_block, end_block);

        // Generate body block
        llvm_builder->SetInsertPoint(body_block);
        visit(ctx->stmt(0));
        if (!llvm_builder->GetInsertBlock()->getTerminator()) {
            llvm_builder->CreateBr(cond_block); // Loop back to condition
        }

        // Pop loop context
        popLoop();

        // Continue with end block
        llvm_builder->SetInsertPoint(end_block);

        indent--;
        printIndent();
        std::cout << ") End While" << std::endl;
    } else if (ctx->BREAK()) {
        // BREAK statement
        // BREAK SEMICOLON
        printIndent();
        std::cout << "Break Stmt" << std::endl;

        llvm::BasicBlock* break_target = getCurrentBreakTarget();
        if (break_target) {
            llvm_stmt = llvm_builder->CreateBr(break_target);
            printIndent();
            std::cout << "Breaking to: " << break_target->getName().str() << std::endl;
        } else {
            std::cerr << "Error: break statement not inside a loop" << std::endl;
            // Create a dummy branch to avoid crashes - this should be caught by semantic analysis
            llvm::BasicBlock* dummy_block = llvm::BasicBlock::Create(*llvm_context, "break.error", current_function);
            llvm_stmt = llvm_builder->CreateBr(dummy_block);
        }

    } else if (ctx->CONTINUE()) {
        // CONTINUE statement
        // CONTINUE SEMICOLON
        printIndent();
        std::cout << "Continue Stmt" << std::endl;

        llvm::BasicBlock* continue_target = getCurrentContinueTarget();
        if (continue_target) {
            llvm_stmt = llvm_builder->CreateBr(continue_target);
            printIndent();
            std::cout << "Continuing to: " << continue_target->getName().str() << std::endl;
        } else {
            std::cerr << "Error: continue statement not inside a loop" << std::endl;
            // Create a dummy branch to avoid crashes - this should be caught by semantic analysis
            llvm::BasicBlock* dummy_block = llvm::BasicBlock::Create(*llvm_context, "continue.error", current_function);
            llvm_stmt = llvm_builder->CreateBr(dummy_block);
        }
    } else if (ctx->block()) {
        printIndent();
        std::cout << "Block Stmt (" << std::endl;
        indent++;
        // Block statement
        // block
        llvm::BasicBlock* block_result = visit(ctx->block());
        // For block statements, we don't need to return an instruction
        llvm_stmt = nullptr;
        indent--;
        printIndent();
        std::cout << ") End Block" << std::endl;
    } else if (ctx->exp()) {
        // Expression statement
        // exp SEMICOLON
        printIndent();
        std::cout << "Expression Stmt (" << std::endl;
        indent++;
        llvm::Value* exp_value = visit(ctx->exp());
        // For expression statements, we don't need to store the result
        // Just evaluate the expression for side effects (like function calls)
        llvm_stmt = nullptr;
        indent--;
        printIndent();
        std::cout << ") End Expression" << std::endl;
    } else {
        std::cerr << "Unknown statement:" << ctx->toString() << std::endl;
    }
    
    return llvm_stmt;
}

/*
exp : addExp;
*/
antlrcpp::Any ASTVisitor::visitExp(SysY2022Parser::ExpContext *ctx) {
    printIndent();
    std::cout << "Expression (" << std::endl;
    indent++;

    llvm::Value* llvm_exp = visit(ctx->addExp());

    indent--;
    printIndent();
    std::cout << ") End Expression" << std::endl;


    return llvm_exp;
}

/*
addExp
    : mulExp
    | addExp ( ADD | SUB ) mulExp;
*/
antlrcpp::Any ASTVisitor::visitAddExp(SysY2022Parser::AddExpContext *ctx) {
    printIndent();
    std::cout << "AddExp (" << std::endl;
    indent++;

    llvm::Value* llvm_addExp = nullptr;

    if (ctx->addExp()) {
        // addExp ( ADD | SUB ) mulExp;
        llvm::Value *left = visit(ctx->addExp());
        llvm::Value *right = visit(ctx->mulExp());
        
        if (ctx->ADD()) {
            llvm_addExp = llvm_builder->CreateAdd(left, right, "addtmp");
        } else if (ctx->SUB()) {
            llvm_addExp = llvm_builder->CreateSub(left, right, "subtmp");
        } else {
            std::cerr << "Unknown operator in AddExp" << std::endl;
        }
    } else if (ctx->mulExp()) {
        llvm_addExp = visit(ctx->mulExp());
    } else {
        std::cerr << "Unknown expression in AddExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End AddExp" << std::endl;
    
    // Single operand
    return llvm_addExp;
}

/*
mulExp
    : unaryExp
    | mulExp ( MUL | DIV | MOD ) unaryExp;
*/
antlrcpp::Any ASTVisitor::visitMulExp(SysY2022Parser::MulExpContext *ctx) {
    printIndent();
    std::cout << "MulExp (" << std::endl;
    indent++;

    llvm::Value* llvm_mulExp = nullptr;

    if (ctx->mulExp()) {
        // mulExp ( MUL | DIV | MOD ) unaryExp;
        llvm::Value *left = visit(ctx->mulExp());
        llvm::Value *right = visit(ctx->unaryExp());
        
        if (ctx->MUL()) {
            llvm_mulExp = llvm_builder->CreateMul(left, right, "multmp");
        } else if (ctx->DIV()) {
            llvm_mulExp = llvm_builder->CreateSDiv(left, right, "divtmp");
        } else if (ctx->MOD()) {
            llvm_mulExp = llvm_builder->CreateSRem(left, right, "modtmp");
        } else {
            std::cerr << "Unknown operator in MulExp" << std::endl;
        }
    } else if (ctx->unaryExp()) {
        llvm_mulExp = visit(ctx->unaryExp());
    } else {
        std::cerr << "Unknown expression in MulExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End MulExp" << std::endl;
    return llvm_mulExp;
}
    
/*
unaryExp
    : primaryExp
    | IDENT LPAREN funcFormalParams? RPAREN
    | unaryOP unaryExp;
*/
antlrcpp::Any ASTVisitor::visitUnaryExp(SysY2022Parser::UnaryExpContext *ctx) {
    printIndent();
    std::cout << "UnaryExp (" << std::endl;
    indent++;

    llvm::Value* llvm_unaryExp = nullptr;

    if (ctx->primaryExp()) {
        // primaryExp
        llvm_unaryExp = visit(ctx->primaryExp());
    }
    if (ctx->IDENT()) {
        // Function call: IDENT LPAREN funcRealParams? RPAREN
        std::string funcName = ctx->IDENT()->getText();
        printIndent();
        std::cout << "Function call: " << funcName << std::endl;

        // Get function from function table
        if (function_table.find(funcName) != function_table.end()) {
            llvm::Function* callee = function_table[funcName];

            // Get arguments
            std::vector<llvm::Value*> args;
            if (ctx->funcRealParams()) {
                auto argsResult = visit(ctx->funcRealParams());
                try {
                    auto argsPtr = argsResult.as<std::shared_ptr<std::vector<llvm::Value*>>>();
                    args = *argsPtr;
                } catch (...) {
                    std::cerr << "Error: Failed to get function arguments" << std::endl;
                }
            }

            // Create function call
            llvm_unaryExp = llvm_builder->CreateCall(callee, args, funcName + "_result");
            printIndent();
            std::cout << "Created function call to: " << funcName << std::endl;
            printIndent();
            std::cout << "Function call result type: " << (llvm_unaryExp ? "valid" : "null") << std::endl;
        } else {
            std::cerr << "Error: Undefined function: " << funcName << std::endl;
        }
    }
    if (ctx->unaryOP()) {
        // unaryOP unaryExp
        auto opResult = visit(ctx->unaryOP());
        // TODO: change this logic
        std::string op;
        try {
            op = opResult.as<std::string>();
        } catch (...) {
            std::cerr << "Error: Failed to get unary operator" << std::endl;
            op = "";
        }

        llvm::Value* operand = visit(ctx->unaryExp());

        if (op == "+") {
            // Unary plus: just return the operand
            llvm_unaryExp = operand;
        } else if (op == "-") {
            // Unary minus: negate the operand
            if (operand->getType()->isFloatTy()) {
                llvm_unaryExp = llvm_builder->CreateFNeg(operand, "fnegtmp");
            } else {
                llvm_unaryExp = llvm_builder->CreateNeg(operand, "negtmp");
            }
        } else if (op == "!") {
            // Logical NOT: convert to boolean and negate
            if (operand->getType()->isIntegerTy(1)) {
                // Already boolean, just negate
                llvm_unaryExp = llvm_builder->CreateNot(operand, "nottmp");
            } else if (operand->getType()->isFloatTy()) {
                // Compare float with 0.0
                llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                llvm::Value* cmp = llvm_builder->CreateFCmpOEQ(operand, zero, "fcmpeq");
                llvm_unaryExp = cmp; // true if operand == 0.0
            } else if (operand->getType()->isIntegerTy()) {
                // Compare integer with 0
                llvm::Value* zero = llvm::ConstantInt::get(operand->getType(), 0);
                llvm::Value* cmp = llvm_builder->CreateICmpEQ(operand, zero, "icmpeq");
                llvm_unaryExp = cmp; // true if operand == 0
            }
        }

        printIndent();
        std::cout << "Applied unary operator: " << op << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End UnaryExp" << std::endl;
    return llvm_unaryExp;
}

/*
primaryExp
    : lVal | number |  LPAREN exp RPAREN;
*/
antlrcpp::Any ASTVisitor::visitPrimaryExp(SysY2022Parser::PrimaryExpContext *ctx) {
    printIndent();
    std::cout << "PrimaryExp (" << std::endl;
    indent++;

    llvm::Value* llvm_primaryExp = nullptr;

    if (ctx->lVal()) {
        // lVal
        llvm_primaryExp = visit(ctx->lVal());
    }
    if (ctx->number()) {
        // number
        llvm_primaryExp = visit(ctx->number());
    }
    if (ctx->exp()) {
        // LPAREN exp RPAREN
        llvm_primaryExp = visit(ctx->exp());
    }

    indent--;
    printIndent();
    std::cout << ") End PrimaryExp" << std::endl;
    return llvm_primaryExp;
}



antlrcpp::Any ASTVisitor::visitNumber(SysY2022Parser::NumberContext *ctx) {
    llvm::Value* llvm_number = nullptr;

    if (ctx->INTCONST()) {
        // Integer constant - handle decimal, hexadecimal, and octal
        std::string text = ctx->INTCONST()->getText();
        int value = 0;

        if (text.length() >= 2 && (text.substr(0, 2) == "0x" || text.substr(0, 2) == "0X")) {
            // Hexadecimal: 0x... or 0X...
            value = std::stoi(text, nullptr, 16);
            printIndent();
            std::cout << "Number (Hex): " << text << " = " << value << std::endl;
        } else if (text.length() >= 2 && text[0] == '0' && text[1] >= '0' && text[1] <= '7') {
            // Octal: 0... (starts with 0 and followed by octal digits)
            value = std::stoi(text, nullptr, 8);
            printIndent();
            std::cout << "Number (Oct): " << text << " = " << value << std::endl;
        } else {
            // Decimal: regular number
            value = std::stoi(text, nullptr, 10);
            printIndent();
            std::cout << "Number (Dec): " << value << std::endl;
        }

        llvm_number = llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), value);
    } else if (ctx->FLOATCONST()) {
        // Float constant
        float value = std::stof(ctx->FLOATCONST()->getText());
        llvm_number = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), value);
        printIndent();
        std::cout << "Number (Float): " << value << std::endl;
    } else {
        std::cerr << "Unknown number type: " << ctx->getText() << std::endl;
    }

    return llvm_number;
}

antlrcpp::Any ASTVisitor::visitLVal(SysY2022Parser::LValContext *ctx) {
    printIndent();
    std::cout << "LVal: ";
    std::string var_name = ctx->IDENT()->getText();
    llvm::Value* l_val = nullptr;

    // Check if variable exists in symbol table
    if (named_values.find(var_name) != named_values.end()) {
        llvm::Value* var = named_values[var_name][0];

        // Check if it's a global constant
        if (auto global_var = llvm::dyn_cast<llvm::GlobalVariable>(var)) {
            l_val = llvm_builder->CreateLoad(global_var->getValueType(), global_var, var_name);
        }
        // Check if it's a local alloca (variable or local constant array)
        else if (auto alloca_inst = llvm::dyn_cast<llvm::AllocaInst>(var)) {
            l_val = llvm_builder->CreateLoad(alloca_inst->getAllocatedType(), alloca_inst, var_name);
        }
        // Check if it's a constant value (scalar constants)
        // This should not happen
        // TODO: delete this branch
        else if (auto constValue = llvm::dyn_cast<llvm::Constant>(var)) {
            // l_val = constValue; // Return constant directly
            std::cerr << "Warning: Accessing constant as lvalue: " << var_name << std::endl;
        }
        else {
            std::cerr << "Unknown variable type for: " << var_name << std::endl;
        }
    } else {
        std::cerr << "Undefined variable: " << var_name << std::endl;
    }

    std::cout << var_name << std::endl;
    return l_val;
}

/*
relExp
    : addExp
    | relExp ( LT | GT | LE | GE ) addExp ;
*/
antlrcpp::Any ASTVisitor::visitRelExp(SysY2022Parser::RelExpContext *ctx) {
    printIndent();
    std::cout << "RelExp (" << std::endl;
    indent++;

    llvm::Value* llvm_relExp = nullptr;

    if (ctx->relExp()) {
        // relExp ( LT | GT | LE | GE ) addExp
        llvm::Value *left = visit(ctx->relExp());
        llvm::Value *right = visit(ctx->addExp());

        // Convert to same type if needed
        // TODO: check this logic
        if (left->getType() != right->getType()) {
            // Handle boolean to integer conversion
            if (left->getType()->isIntegerTy(1) && right->getType()->isIntegerTy()) {
                // Convert boolean to integer
                left = llvm_builder->CreateZExt(left, right->getType(), "bool2int");
            } else if (right->getType()->isIntegerTy(1) && left->getType()->isIntegerTy()) {
                // Convert boolean to integer
                right = llvm_builder->CreateZExt(right, left->getType(), "bool2int");
            } else if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                // Convert to float
                if (left->getType()->isIntegerTy()) {
                    left = llvm_builder->CreateSIToFP(left, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                if (right->getType()->isIntegerTy()) {
                    right = llvm_builder->CreateSIToFP(right, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
            }
        }

        if (ctx->LT()) {
            if (left->getType()->isFloatTy()) {
                llvm_relExp = llvm_builder->CreateFCmpOLT(left, right, "fcmplt");
            } else {
                llvm_relExp = llvm_builder->CreateICmpSLT(left, right, "icmplt");
            }
        } else if (ctx->GT()) {
            if (left->getType()->isFloatTy()) {
                llvm_relExp = llvm_builder->CreateFCmpOGT(left, right, "fcmpgt");
            } else {
                llvm_relExp = llvm_builder->CreateICmpSGT(left, right, "icmpgt");
            }
        } else if (ctx->LE()) {
            if (left->getType()->isFloatTy()) {
                llvm_relExp = llvm_builder->CreateFCmpOLE(left, right, "fcmple");
            } else {
                llvm_relExp = llvm_builder->CreateICmpSLE(left, right, "icmple");
            }
        } else if (ctx->GE()) {
            if (left->getType()->isFloatTy()) {
                llvm_relExp = llvm_builder->CreateFCmpOGE(left, right, "fcmpge");
            } else {
                llvm_relExp = llvm_builder->CreateICmpSGE(left, right, "icmpge");
            }
        } else {
            std::cerr << "Unknown relational operator in RelExp" << std::endl;
        }
    } else if (ctx->addExp()) {
        // addExp
        llvm_relExp = visit(ctx->addExp());
    } else {
        std::cerr << "Unknown expression in RelExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End RelExp" << std::endl;
    return llvm_relExp;
}

/*
eqExp
    : relExp
    | eqExp ( EQ | NE ) relExp;
*/
antlrcpp::Any ASTVisitor::visitEqExp(SysY2022Parser::EqExpContext *ctx) {
    printIndent();
    std::cout << "EqExp (" << std::endl;
    indent++;

    llvm::Value* llvm_eqExp = nullptr;

    if (ctx->eqExp()) {
        // eqExp ( EQ | NE ) relExp
        llvm::Value *left = visit(ctx->eqExp());
        llvm::Value *right = visit(ctx->relExp());

        // Convert to same type if needed
        // TODO: check this logic
        if (left->getType() != right->getType()) {
            // Handle boolean to integer conversion
            if (left->getType()->isIntegerTy(1) && right->getType()->isIntegerTy()) {
                // Convert boolean to integer
                left = llvm_builder->CreateZExt(left, right->getType(), "bool2int");
            } else if (right->getType()->isIntegerTy(1) && left->getType()->isIntegerTy()) {
                // Convert boolean to integer
                right = llvm_builder->CreateZExt(right, left->getType(), "bool2int");
            } else if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                // Convert to float
                if (left->getType()->isIntegerTy()) {
                    left = llvm_builder->CreateSIToFP(left, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                if (right->getType()->isIntegerTy()) {
                    right = llvm_builder->CreateSIToFP(right, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
            }
        }

        if (ctx->EQ()) {
            if (left->getType()->isFloatTy()) {
                llvm_eqExp = llvm_builder->CreateFCmpOEQ(left, right, "fcmpeq");
            } else {
                llvm_eqExp = llvm_builder->CreateICmpEQ(left, right, "icmpeq");
            }
        } else if (ctx->NE()) {
            if (left->getType()->isFloatTy()) {
                llvm_eqExp = llvm_builder->CreateFCmpONE(left, right, "fcmpne");
            } else {
                llvm_eqExp = llvm_builder->CreateICmpNE(left, right, "icmpne");
            }
        } else {
            std::cerr << "Unknown equality operator in EqExp" << std::endl;
        }
    } else if (ctx->relExp()) {
        // relExp
        llvm_eqExp = visit(ctx->relExp());
    } else {
        std::cerr << "Unknown expression in EqExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End EqExp" << std::endl;
    return llvm_eqExp;
}

/*
lAndExp
    : eqExp
    | lAndExp AND eqExp;
*/
antlrcpp::Any ASTVisitor::visitLAndExp(SysY2022Parser::LAndExpContext *ctx) {
    printIndent();
    std::cout << "LAndExp (" << std::endl;
    indent++;

    llvm::Value* llvm_lAndExp = nullptr;

    if (ctx->lAndExp()) {
        // lAndExp AND eqExp
        llvm::Value *left = visit(ctx->lAndExp());
        llvm::Value *right = visit(ctx->eqExp());

        // Convert to boolean (i1) if needed
        // TODO: check the logic
        if (left->getType() != llvm::Type::getInt1Ty(*llvm_context)) {
            if (left->getType()->isFloatTy()) {
                // Compare float with 0.0
                llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                left = llvm_builder->CreateFCmpONE(left, zero, "ftobool");
            } else if (left->getType()->isIntegerTy()) {
                // Compare integer with 0
                llvm::Value* zero = llvm::ConstantInt::get(left->getType(), 0);
                left = llvm_builder->CreateICmpNE(left, zero, "itobool");
            }
        }

        if (right->getType() != llvm::Type::getInt1Ty(*llvm_context)) {
            if (right->getType()->isFloatTy()) {
                // Compare float with 0.0
                llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                right = llvm_builder->CreateFCmpONE(right, zero, "ftobool");
            } else if (right->getType()->isIntegerTy()) {
                // Compare integer with 0
                llvm::Value* zero = llvm::ConstantInt::get(right->getType(), 0);
                right = llvm_builder->CreateICmpNE(right, zero, "itobool");
            }
        }

        llvm_lAndExp = llvm_builder->CreateAnd(left, right, "andtmp");
    } else if (ctx->eqExp()) {
        // eqExp
        llvm_lAndExp = visit(ctx->eqExp());
    } else {
        std::cerr << "Unknown expression in LAndExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End LAndExp" << std::endl;
    return llvm_lAndExp;
}

/*
lOrExp
    : lAndExp
    | lOrExp OR lAndExp;
*/
antlrcpp::Any ASTVisitor::visitLOrExp(SysY2022Parser::LOrExpContext *ctx) {
    printIndent();
    std::cout << "LOrExp (" << std::endl;
    indent++;

    llvm::Value* llvm_lOrExp = nullptr;

    if (ctx->lOrExp()) {
        // lOrExp OR lAndExp
        llvm::Value *left = visit(ctx->lOrExp());
        llvm::Value *right = visit(ctx->lAndExp());

        // Convert to boolean (i1) if needed
        if (left->getType() != llvm::Type::getInt1Ty(*llvm_context)) {
            if (left->getType()->isFloatTy()) {
                // Compare float with 0.0
                llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                left = llvm_builder->CreateFCmpONE(left, zero, "ftobool");
            } else if (left->getType()->isIntegerTy()) {
                // Compare integer with 0
                llvm::Value* zero = llvm::ConstantInt::get(left->getType(), 0);
                left = llvm_builder->CreateICmpNE(left, zero, "itobool");
            }
        }

        if (right->getType() != llvm::Type::getInt1Ty(*llvm_context)) {
            if (right->getType()->isFloatTy()) {
                // Compare float with 0.0
                llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                right = llvm_builder->CreateFCmpONE(right, zero, "ftobool");
            } else if (right->getType()->isIntegerTy()) {
                // Compare integer with 0
                llvm::Value* zero = llvm::ConstantInt::get(right->getType(), 0);
                right = llvm_builder->CreateICmpNE(right, zero, "itobool");
            }
        }

        llvm_lOrExp = llvm_builder->CreateOr(left, right, "ortmp");
    } else if (ctx->lAndExp()) {
        // lAndExp
        llvm_lOrExp = visit(ctx->lAndExp());
    } else {
        std::cerr << "Unknown expression in LOrExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End LOrExp" << std::endl;
    return llvm_lOrExp;
}
