// Test hexadecimal and octal number support
int main() {
    // Test 1: Hexadecimal numbers (lowercase x)
    int hex1 = 0x10;        // 16 in decimal
    int hex2 = 0xff;        // 255 in decimal
    int hex3 = 0xabc;       // 2748 in decimal
    int hex4 = 0x123;       // 291 in decimal
    
    // Test 2: Hexadecimal numbers (uppercase X)
    int hex5 = 0X10;        // 16 in decimal
    int hex6 = 0XFF;        // 255 in decimal
    int hex7 = 0XABC;       // 2748 in decimal
    
    // Test 3: Hexadecimal with mixed case
    int hex8 = 0xAbC;       // 2748 in decimal
    int hex9 = 0XaBc;       // 2748 in decimal
    
    // Test 4: Octal numbers
    int oct1 = 010;         // 8 in decimal
    int oct2 = 077;         // 63 in decimal
    int oct3 = 0123;        // 83 in decimal
    int oct4 = 0777;        // 511 in decimal
    
    // Test 5: Decimal numbers (for comparison)
    int dec1 = 10;          // 10 in decimal
    int dec2 = 255;         // 255 in decimal
    int dec3 = 123;         // 123 in decimal
    
    // Test 6: Zero in different formats
    int zero_dec = 0;       // Decimal zero
    int zero_hex = 0x0;     // Hexadecimal zero
    int zero_oct = 00;      // Octal zero
    
    // Test 7: Large numbers
    int large_hex = 0xFFFF;     // 65535 in decimal
    int large_oct = 07777;      // 4095 in decimal
    
    // Test 8: Single digit numbers
    int single_hex = 0xF;       // 15 in decimal
    int single_oct = 07;        // 7 in decimal
    
    // Test 9: Use in expressions
    int sum_hex = 0x10 + 0x20;  // 16 + 32 = 48
    int sum_oct = 010 + 020;    // 8 + 16 = 24
    int sum_mixed = 0x10 + 010 + 10;  // 16 + 8 + 10 = 34
    
    // Test 10: Use in array sizes
    int hex_array[0x5];         // Array of size 5
    int oct_array[010];         // Array of size 8
    
    // Test 11: Use in initialization
    int mixed_array[3] = {0x10, 010, 10};  // {16, 8, 10}
    
    // Test 12: Constants with different formats
    const int CONST_HEX = 0xFF;
    const int CONST_OCT = 077;
    const int CONST_DEC = 255;
    
    // Calculate result using different number formats
    int result = hex1 + oct1 + dec1;  // 16 + 8 + 10 = 34
    result = result + sum_mixed;      // 34 + 34 = 68
    result = result + CONST_HEX;      // 68 + 255 = 323
    
    return result;
}
