// Test break and continue statements in loops
int main() {
    int result = 0;
    
    // Test 1: Simple break in while loop
    int i = 0;
    while (i < 10) {
        if (i == 5) {
            break;  // Should exit loop when i == 5
        }
        result = result + i;
        i = i + 1;
    }
    // result should be 0+1+2+3+4 = 10
    
    // Test 2: Simple continue in while loop
    int j = 0;
    while (j < 5) {
        j = j + 1;
        if (j == 3) {
            continue;  // Skip when j == 3
        }
        result = result + j;
    }
    // Should add 1+2+4+5 = 12, so result = 10+12 = 22
    
    // Test 3: Break and continue with blocks
    int k = 0;
    while (k < 8) {
        k = k + 1;
        {
            int local = k * 2;
            if (local > 10) {
                break;  // Break from inner block
            }
            if (local == 6) {
                continue;  // Continue from inner block
            }
            result = result + local;
        }
    }
    // Should add 2+4+8+10 = 24, so result = 22+24 = 46
    
    // Test 4: Nested loops with break/continue
    int outer = 0;
    while (outer < 3) {
        outer = outer + 1;
        int inner = 0;
        while (inner < 4) {
            inner = inner + 1;
            if (inner == 2) {
                continue;  // Continue inner loop
            }
            if (outer == 2 && inner == 3) {
                break;  // Break inner loop
            }
            result = result + (outer * 10 + inner);
        }
    }
    // Should add: 11+13+14 + 21 + 31+33+34 = 157, so result = 46+157 = 203
    
    return result;
}
