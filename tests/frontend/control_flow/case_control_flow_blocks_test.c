// Test control flow with blocks
int main() {
    int result = 0;
    int condition = 1;
    
    // Test 1: If-else with blocks
    if (condition > 0) {
        int positive_value = 10;
        result = result + positive_value;
        
        // Nested if in block
        if (positive_value > 5) {
            int bonus = 5;
            result = result + bonus;
        }
    } else {
        int negative_value = -10;
        result = result + negative_value;
    }
    
    // Test 2: While loop with block
    int counter = 0;
    while (counter < 3) {
        int iteration_value = counter * 10;
        result = result + iteration_value;
        counter = counter + 1;
        
        // Nested block in while
        {
            int temp = iteration_value / 2;
            result = result + temp;
        }
    }
    
    // Test 3: Nested if-else in while
    int i = 0;
    while (i < 2) {
        if (i == 0) {
            int first_iteration = 100;
            result = result + first_iteration;
        } else {
            int second_iteration = 200;
            result = result + second_iteration;
        }
        i = i + 1;
    }
    
    // Test 4: Complex nested structure
    {
        int outer = 1;
        if (outer > 0) {
            int middle = 2;
            while (middle > 0) {
                int inner = 3;
                result = result + (outer + middle + inner);
                middle = middle - 1;
                
                {
                    int deepest = 4;
                    result = result + deepest;
                }
            }
        }
    }
    
    return result;
}
