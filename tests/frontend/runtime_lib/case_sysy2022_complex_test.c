// Test complex usage of SysY 2022 runtime library functions

// Function that uses runtime library for output
void print_message() {
    putch(72);  // 'H'
    putch(101); // 'e'
    putch(108); // 'l'
    putch(108); // 'l'
    putch(111); // 'o'
    putch(32);  // ' '
    putch(83);  // 'S'
    putch(121); // 'y'
    putch(115); // 's'
    putch(89);  // 'Y'
    putch(10);  // '\n'
}

// Function that processes integer array input/output
int process_int_array() {
    int arr[10];
    int count = getarray(arr);  // Read array from input
    
    // Process array (double each element)
    int i = 0;
    while (i < count) {
        arr[i] = arr[i] * 2;
        i = i + 1;
    }
    
    // Output processed array
    putarray(count, arr);
    
    // Return sum of processed array
    int sum = 0;
    i = 0;
    while (i < count) {
        sum = sum + arr[i];
        i = i + 1;
    }
    
    return sum;
}

// Function that processes float array
float process_float_array() {
    float farr[10];
    int count = getfarray(farr);  // Read float array from input
    
    // Process array (halve each element)
    int i = 0;
    while (i < count) {
        farr[i] = farr[i] / 2.0;
        i = i + 1;
    }
    
    // Output processed array
    putfarray(count, farr);
    
    // Return average of processed array
    float sum = 0.0;
    i = 0;
    while (i < count) {
        sum = sum + farr[i];
        i = i + 1;
    }
    
    return sum / count;
}

// Function that does timed computation
int timed_computation(int n) {
    starttime();
    
    // Compute factorial
    int result = 1;
    int i = 1;
    while (i <= n) {
        result = result * i;
        i = i + 1;
    }
    
    stoptime();
    
    return result;
}

// Function that mixes different I/O types
void mixed_io_demo() {
    // Get inputs
    int int_val = getint();
    float float_val = getfloat();
    int char_val = getch();
    
    // Process and output
    putint(int_val * 2);
    putch(32);  // space
    
    putfloat(float_val / 2.0);
    putch(32);  // space
    
    putch(char_val);
    putch(10);  // newline
}

// Recursive function using runtime library
int recursive_countdown(int n) {
    if (n <= 0) {
        return 0;
    }
    
    putint(n);
    putch(32);  // space
    
    return n + recursive_countdown(n - 1);
}

// Main function testing all scenarios
int main() {
    // Test 1: Simple function call with runtime library
    print_message();
    
    // Test 2: Integer array processing
    putch(73);  // 'I'
    putch(110); // 'n'
    putch(116); // 't'
    putch(32);  // ' '
    putch(65);  // 'A'
    putch(114); // 'r'
    putch(114); // 'r'
    putch(97);  // 'a'
    putch(121); // 'y'
    putch(58);  // ':'
    putch(10);  // '\n'
    
    int array_sum = process_int_array();
    putint(array_sum);
    putch(10);  // '\n'
    
    // Test 3: Float array processing
    putch(70);  // 'F'
    putch(108); // 'l'
    putch(111); // 'o'
    putch(97);  // 'a'
    putch(116); // 't'
    putch(32);  // ' '
    putch(65);  // 'A'
    putch(114); // 'r'
    putch(114); // 'r'
    putch(97);  // 'a'
    putch(121); // 'y'
    putch(58);  // ':'
    putch(10);  // '\n'
    
    float array_avg = process_float_array();
    putfloat(array_avg);
    putch(10);  // '\n'
    
    // Test 4: Timed computation
    int factorial_result = timed_computation(5);
    putint(factorial_result);
    putch(10);  // '\n'
    
    // Test 5: Mixed I/O
    mixed_io_demo();
    
    // Test 6: Recursive function with I/O
    int recursive_sum = recursive_countdown(5);
    putch(10);  // '\n'
    putint(recursive_sum);
    putch(10);  // '\n'
    
    // Test 7: Complex expression with runtime calls
    int complex_result = getint() + timed_computation(3) + array_sum;
    putint(complex_result);
    
    return complex_result;
}
