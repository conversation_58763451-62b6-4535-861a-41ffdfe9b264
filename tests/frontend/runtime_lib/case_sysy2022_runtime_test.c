// Test SysY 2022 runtime library functions
int main() {
    // Test 1: Basic I/O functions
    int ch = getch();       // Read a character (returns ASCII code)
    putch(ch);             // Write the character back
    
    // Test 2: Integer I/O
    int num = getint();     // Read an integer
    putint(num);           // Write the integer
    
    // Test 3: Float I/O
    float f = getfloat();   // Read a float
    putfloat(f);           // Write the float
    
    // Test 4: Integer array I/O
    int arr[5] = {1, 2, 3, 4, 5};
    putarray(5, arr);      // Print integer array
    
    int input_arr[10];
    int count = getarray(input_arr);   // Read array, returns count
    putint(count);         // Output how many elements were read
    
    // Test 5: Float array I/O
    float farr[3] = {1.1, 2.2, 3.3};
    putfarray(3, farr);    // Print float array
    
    float input_farr[10];
    int fcount = getfarray(input_farr);  // Read float array, returns count
    putint(fcount);        // Output how many float elements were read
    
    // Test 6: Timing functions
    starttime();           // Start timing
    
    // Do some computation
    int result = 0;
    int i = 0;
    while (i < 1000) {
        result = result + i;
        i = i + 1;
    }
    
    stoptime();            // Stop timing and output elapsed time
    
    // Test 7: Character output with loop
    int loop_count = 0;
    while (loop_count < 5) {
        putch(65 + loop_count);  // Print A, B, C, D, E
        loop_count = loop_count + 1;
    }
    putch(10);  // newline
    
    // Test 8: Number output with calculations
    int x = 10;
    int y = 20;
    putint(x + y);         // Print 30
    putch(10);  // newline
    
    // Test 9: Float calculations
    float a = 3.14;
    float b = 2.0;
    putfloat(a * b);       // Print 6.28
    putch(10);  // newline
    
    return result;
}
