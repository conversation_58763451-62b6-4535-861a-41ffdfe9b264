// Test variable declarations to verify visitVarDecl functionality
int main() {
    // Test 1: Simple scalar variables
    int a;
    float b;
    
    // Test 2: Scalar variables with initialization
    int c = 10;
    float d = 3.14;
    
    // Test 3: Multiple variables in one declaration
    int e, f, g;
    int h = 1, i = 2, j = 3;
    
    // Test 4: Array variables
    int arr1[5];
    float arr2[10];
    
    // Test 5: Array variables with initialization
    int arr3[3] = {1, 2, 3};
    float arr4[2] = {1.1, 2.2};
    
    // Test 6: Multi-dimensional arrays
    int matrix[2][3];
    int matrix2[2][3] = {{1, 2, 3}, {4, 5, 6}};
    
    // Test 7: Mixed declarations
    int x = 42, y[5], z = arr3[0];
    
    return c + h;
}
