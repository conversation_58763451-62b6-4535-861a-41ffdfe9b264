// Test mixed int and float types
int main() {
    // Test 1: Mixed scalar variables
    int int_var = 42;
    float float_var = 3.14;
    
    // Test 2: Mixed constant scalars
    const int INT_CONST = 100;
    const float FLOAT_CONST = 2.718;
    
    // Test 3: Mixed arrays
    int int_arr[3] = {1, 2, 3};
    float float_arr[3] = {1.1, 2.2, 3.3};
    
    // Test 4: Mixed constant arrays
    const int const_int_arr[2] = {10, 20};
    const float const_float_arr[2] = {1.5, 2.5};
    
    // Test 5: Mixed 2D arrays
    int int_matrix[2][2] = {{1, 2}, {3, 4}};
    float float_matrix[2][2] = {{1.1, 1.2}, {2.1, 2.2}};
    
    // Test 6: Mixed constant 2D arrays
    const int const_int_matrix[2][2] = {{5, 6}, {7, 8}};
    const float const_float_matrix[2][2] = {{0.5, 0.6}, {0.7, 0.8}};
    
    // Test 7: Large mixed arrays
    int large_int[10] = {1, 2, 3, 4, 5};
    float large_float[10] = {1.1, 2.2, 3.3, 4.4, 5.5};
    
    // Test 8: Mixed 3D arrays
    int int_cube[2][2][2] = {{{1, 2}, {3, 4}}, {{5, 6}, {7, 8}}};
    float float_cube[2][2][2] = {{{1.1, 1.2}, {1.3, 1.4}}, {{2.1, 2.2}, {2.3, 2.4}}};
    
    return int_var;
}
