// Test constant arrays with constant expressions
int main() {
    // Test 1: Array size from constant expressions
    const int SIZE = 5;
    const int arr1[SIZE] = {1, 2, 3, 4, 5};
    
    // Test 2: Array size from arithmetic expressions
    const int arr2[2 + 3] = {10, 20, 30, 40, 50};
    
    // Test 3: Array initialization with constant expressions
    const int arr3[4] = {1 + 2, 3 * 4, 5 - 1, 6 / 2};
    
    // Test 4: Mixed constant expressions
    const int BASE = 10;
    const int arr4[3] = {BASE, BASE + 5, BASE * 2};
    
    // Test 5: Nested constant expressions in 2D array
    const int matrix[2][2] = {{1 + 1, 2 * 2}, {3 + 3, 4 * 4}};
    
    return arr1[0] + arr3[1] + matrix[1][1];
}
