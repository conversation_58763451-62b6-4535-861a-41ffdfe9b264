// Test global constant arrays
const int global_array[5] = {1, 2, 3, 4, 5};
const float global_floats[3] = {1.1, 2.2, 3.3};
const int global_2d[2][3] = {{1, 2, 3}, {4, 5, 6}};

// Test mixed global and local constants
const int GLOBAL_SIZE = 4;

int main() {
    // Local constant arrays
    const int local_array[3] = {10, 20, 30};
    const int sized_array[GLOBAL_SIZE] = {100, 200, 300, 400};
    
    // Access both global and local arrays
    int sum = global_array[0] + local_array[0];
    sum = sum + global_2d[1][2];
    sum = sum + sized_array[3];
    
    return sum;
}

// Test function with constant array parameter (if supported)
int process_array() {
    const int temp[2] = {global_array[0], global_array[4]};
    return temp[0] + temp[1];
}
