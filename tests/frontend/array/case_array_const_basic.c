// Test basic constant array definitions
int main() {
    // Test 1: Simple 1D constant array
    const int a[5] = {1, 2, 3, 4, 5};
    
    // Test 2: Constant array with partial initialization
    const int b[10] = {1, 2, 3};
    
    // Test 3: Empty initialization
    // const int c[3] = {};
    
    // Test 4: Single element array
    // const int d[1] = {42};
    
    // Test 5: Float constant array
    const float e[4] = {1.0, 2.5, 3.14, 4.2};
    
    // Test 6: Access array elements
    // int result = a[0] + a[4];
    
    // return result;
    return 0;
}
