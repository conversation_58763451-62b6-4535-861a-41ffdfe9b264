// Test 2D constant array definitions
int main() {
    // Test 1: 2D constant array with full initialization
    const int matrix[2][3] = {{1, 2, 3}, {4, 5, 6}};
    
    // Test 2: 2D array with partial initialization
    const int partial[3][2] = {{1, 2}, {3}};
    
    // Test 3: 2D array with empty initialization
    const int empty[2][2] = {};
    
    // Test 4: Access 2D array elements
    int sum = matrix[0][0] + matrix[1][2];
    
    // Test 5: 3D constant array (small)
    const int cube[2][2][2] = {{{1, 2}, {3, 4}}, {{5, 6}, {7, 8}}};
    
    return 0;
}
