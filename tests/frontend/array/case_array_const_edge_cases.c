// Test edge cases for constant arrays
int main() {
    // Test 1: Large array (test memory allocation)
    const int large[100] = {1, 2, 3, 4, 5}; // Rest should be zero-initialized
    
    // Test 2: Array with all same values
    const int same[5] = {42, 42, 42, 42, 42};
    
    // Test 3: Array with negative values
    const int negative[4] = {-1, -10, -100, -1000};
    
    // Test 4: Array with zero values
    const int zeros[3] = {0, 0, 0};
    
    // Test 5: Mixed positive and negative
    const int mixed[6] = {-5, -1, 0, 1, 5, 10};
    
    // Test 6: Hexadecimal and octal values
    const int hex_oct[4] = {0xFF, 0x10, 077, 010};
    
    // Test 7: Single large value
    const int big_val[1] = {2147483647}; // INT_MAX
    
    // Test 8: Float edge cases
    const float float_edge[4] = {0.0, -0.0, 1e-10, 1e10};
    
    // Access some elements to test functionality
    int result = large[0] + negative[3] + mixed[5];
    
    return result;
}
