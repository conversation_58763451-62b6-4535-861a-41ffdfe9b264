// Comprehensive test for float support in variables and constants
int main() {
    // Test 1: Float scalar variables
    float a;
    float b = 3.14;
    float c = 2.5;
    
    // Test 2: Float constant scalars
    const float PI = 3.14159;
    const float E = 2.71828;
    
    // Test 3: Float 1D arrays
    float arr1[5];
    float arr2[3] = {1.1, 2.2, 3.3};
    
    // Test 4: Float constant 1D arrays
    const float const_arr1[4] = {1.0, 2.0, 3.0, 4.0};
    const float const_arr2[2] = {PI, E};
    
    // Test 5: Float 2D arrays
    float matrix[2][3];
    float matrix2[2][2] = {{1.1, 1.2}, {2.1, 2.2}};
    
    // Test 6: Float constant 2D arrays
    const float const_matrix[2][3] = {{1.1, 1.2, 1.3}, {2.1, 2.2, 2.3}};
    
    // Test 7: Float 3D arrays
    float cube[2][2][2];
    float cube2[2][2][2] = {{{1.1, 1.2}, {1.3, 1.4}}, {{2.1, 2.2}, {2.3, 2.4}}};
    
    // Test 8: Float constant 3D arrays
    const float const_cube[2][2][2] = {{{0.1, 0.2}, {0.3, 0.4}}, {{0.5, 0.6}, {0.7, 0.8}}};
    
    // Test 9: Mixed declarations
    float x = 1.5, y[3] = {1.0, 2.0, 3.0}, z = arr2[0];
    
    // Test 10: Empty initialization
    float empty_arr[5] = {};
    const float const_empty[3] = {};
    
    return 0;
}
