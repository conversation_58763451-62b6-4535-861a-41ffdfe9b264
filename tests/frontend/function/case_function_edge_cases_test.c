// Test edge cases for function declarations
// Function with no return statement (should add default return)
int no_explicit_return(int x) {
    int temp = x * 2;
    // No return statement - should return 0 by default
}

// Void function with no return
void void_no_return() {
    int temp = 42;
    // No return statement - should add return void
}

// Function with early return
int early_return(int x) {
    if (x > 10) {
        return x * 2;
    }
    return x + 1;
}

// Function with multiple returns
int multiple_returns(int x) {
    if (x < 0) {
        return -1;
    } else if (x == 0) {
        return 0;
    } else {
        return 1;
    }
}

// Function with parameter assignment
int modify_param(int x) {
    x = x + 10;  // Modify parameter
    return x;
}

// Function with array parameter and modification
int modify_array_param(int arr[]) {
    arr[0] = 999;  // Modify array element
    return arr[0];
}

// Function with local variables shadowing parameters
int shadow_param(int x) {
    {
        int x = 100;  // Shadow parameter
        return x;     // Should return 100, not the parameter
    }
}

// Function with complex expressions in parameters
int complex_param_expr(int a, int b) {
    return (a + b) * (a - b);
}

// Function calling itself indirectly
int even(int n);
int odd(int n);

int even(int n) {
    if (n == 0) {
        return 1;
    } else {
        return odd(n - 1);
    }
}

int odd(int n) {
    if (n == 0) {
        return 0;
    } else {
        return even(n - 1);
    }
}

// Function with nested function calls
int nested_calls(int x) {
    return add_one(add_one(add_one(x)));
}

// Helper function for nested_calls
int add_one(int x) {
    return x + 1;
}

// Function with hex and octal parameters
int hex_oct_params(int hex_val, int oct_val) {
    return hex_val + oct_val;
}

// Main function testing edge cases
int main() {
    // Test function with no explicit return
    int result1 = no_explicit_return(5);  // Should return 0
    
    // Test void function
    void_no_return();
    
    // Test early return
    int result2 = early_return(15);  // Should return 30
    int result3 = early_return(5);   // Should return 6
    
    // Test multiple returns
    int result4 = multiple_returns(-5);  // Should return -1
    int result5 = multiple_returns(0);   // Should return 0
    int result6 = multiple_returns(5);   // Should return 1
    
    // Test parameter modification
    int result7 = modify_param(10);  // Should return 20
    
    // Test array parameter modification
    int test_array[3] = {1, 2, 3};
    int result8 = modify_array_param(test_array);  // Should return 999
    
    // Test parameter shadowing
    int result9 = shadow_param(50);  // Should return 100
    
    // Test complex parameter expressions
    int result10 = complex_param_expr(5, 3);  // Should return 16
    
    // Test mutual recursion
    int result11 = even(4);  // Should return 1
    int result12 = odd(4);   // Should return 0
    
    // Test nested function calls
    int result13 = nested_calls(10);  // Should return 13
    
    // Test hex and octal parameters
    int result14 = hex_oct_params(0x10, 010);  // Should return 24 (16 + 8)
    
    // Calculate final result
    int final_result = result1 + result2 + result3 + result4 + result5 + 
                      result6 + result7 + result8 + result9 + result10 + 
                      result11 + result12 + result13 + result14;
    
    return final_result;
}
