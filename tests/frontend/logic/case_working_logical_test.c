// Test working logical expressions
int main() {
    int a = 5;
    int b = 10;
    int c = 0;
    
    // Test 1: Simple comparisons in if conditions (WORKING)
    if (a < b) {
        putint(1);  // Should print 1
    }
    
    if (a > b) {
        putint(2);  // Should NOT print
    }
    
    // Test 2: Logical AND (WORKING)
    if (a < b && a > c) {
        putint(3);  // Should print 3
    }
    
    // Test 3: Logical OR
    if (a > b || a < b) {
        putint(4);  // Should print 4
    }
    
    // Test 4: Simple logical NOT (WORKING)
    if (!c) {
        putint(5);  // Should print 5 (because c is 0)
    }
    
    // Test 5: Equality expressions
    if (a == 5) {
        putint(6);  // Should print 6
    }
    
    if (a != b) {
        putint(7);  // Should print 7
    }
    
    return 0;
}
