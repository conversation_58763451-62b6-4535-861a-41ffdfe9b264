// Test logical expressions and comparisons
int main() {
    // Test 1: Basic relational expressions
    int a = 10;
    int b = 20;
    
    // Less than
    if (a < b) {
        putint(1);  // Should print 1 (true)
    }
    
    // Greater than
    if (b > a) {
        putint(2);  // Should print 2 (true)
    }
    
    // Less than or equal
    if (a <= b) {
        putint(3);  // Should print 3 (true)
    }
    
    // Greater than or equal
    if (b >= a) {
        putint(4);  // Should print 4 (true)
    }
    
    // Test 2: Equality expressions
    int x = 5;
    int y = 5;
    int z = 7;
    
    // Equal
    if (x == y) {
        putint(5);  // Should print 5 (true)
    }
    
    // Not equal
    if (x != z) {
        putint(6);  // Should print 6 (true)
    }
    
    // Test 3: Logical AND expressions
    if (a < b && x == y) {
        putint(7);  // Should print 7 (true)
    }
    
    if (a > b && x == y) {
        putint(8);  // Should NOT print (false)
    }
    
    // Test 4: Logical OR expressions
    if (a > b || x == y) {
        putint(9);  // Should print 9 (true)
    }
    
    if (a > b || x != y) {
        putint(10); // Should NOT print (false)
    }
    
    // Test 5: Complex logical expressions
    if ((a < b && x == y) || z > x) {
        putint(11); // Should print 11 (true)
    }
    
    if ((a > b && x != y) || z < x) {
        putint(12); // Should NOT print (false)
    }
    
    // Test 6: Unary operators
    int neg = -a;
    if (neg < 0) {
        putint(13); // Should print 13 (true)
    }
    
    int pos = +b;
    if (pos > 0) {
        putint(14); // Should print 14 (true)
    }
    
    // Test 7: Logical NOT
    if (!(a > b)) {
        putint(15); // Should print 15 (true, because a > b is false)
    }
    
    if (!(a < b)) {
        putint(16); // Should NOT print (false, because a < b is true)
    }
    
    // Test 8: Float comparisons
    float fa = 3.14;
    float fb = 2.71;
    
    if (fa > fb) {
        putint(17); // Should print 17 (true)
    }
    
    if (fa == fb) {
        putint(18); // Should NOT print (false)
    }
    
    // Test 9: Mixed int and float comparisons
    if (a > fa) {
        putint(19); // Should print 19 (true, 10 > 3.14)
    }
    
    // Test 10: Complex nested conditions
    if (((a < b) && (x == y)) || ((fa > fb) && !(z < x))) {
        putint(20); // Should print 20 (true)
    }
    
    return 0;
}
