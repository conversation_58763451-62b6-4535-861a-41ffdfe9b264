// Test unary operators
int main() {
    // Test 1: Unary plus
    int a = 5;
    int pos_a = +a;
    putint(pos_a);  // Should print 5
    
    // Test 2: Unary minus
    int neg_a = -a;
    putint(neg_a);  // Should print -5
    
    // Test 3: <PERSON><PERSON> minus with expressions
    int b = 10;
    int result = -(a + b);
    putint(result); // Should print -15
    
    // Test 4: Logical NOT with integers
    int zero = 0;
    int nonzero = 42;
    
    if (!zero) {
        putint(1);  // Should print 1 (true, because !0 is true)
    }
    
    if (!nonzero) {
        putint(2);  // Should NOT print (false, because !42 is false)
    }
    
    // Test 5: Logical NOT with expressions
    if (!(a > b)) {
        putint(3);  // Should print 3 (true, because 5 > 10 is false)
    }
    
    if (!(a < b)) {
        putint(4);  // Should NOT print (false, because 5 < 10 is true)
    }
    
    // Test 6: Float unary operators
    float fa = 3.14;
    float neg_fa = -fa;
    putfloat(neg_fa);  // Should print -3.14
    
    float pos_fa = +fa;
    putfloat(pos_fa);  // Should print 3.14
    
    // Test 7: Logical NOT with floats
    float fzero = 0.0;
    float fnonzero = 2.71;
    
    if (!fzero) {
        putint(5);  // Should print 5 (true, because !0.0 is true)
    }
    
    if (!fnonzero) {
        putint(6);  // Should NOT print (false, because !2.71 is false)
    }
    
    // Test 8: Nested unary operators
    int c = 7;
    int double_neg = -(-c);
    putint(double_neg);  // Should print 7
    
    // Test 9: Unary operators in expressions
    int d = 3;
    int e = 4;
    int complex_result = -d + -e;
    putint(complex_result);  // Should print -7 (-3 + -4)
    
    // Test 10: Logical NOT in complex expressions
    if (!((a > b) && (d < e))) {
        putint(7);  // Should print 7 (true, because (5>10 && 3<4) is false)
    }
    
    if (!((a < b) || (d > e))) {
        putint(8);  // Should NOT print (false, because (5<10 || 3>4) is true)
    }
    
    // Test 11: Function calls with unary operators
    int input = getint();
    putint(-input);     // Output negative of input
    putint(+input);     // Output positive of input
    
    if (!input) {
        putint(9);      // Print 9 if input is zero
    }
    
    return 0;
}
