cmake_minimum_required(VERSION 3.16)
project(SysY2022Compiler)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find ANTLR4 runtime manually
find_path(ANTLR4_INCLUDE_DIR 
    NAMES antlr4-runtime.h
    PATHS /usr/include/antlr4-runtime /usr/local/include/antlr4-runtime
)

find_library(ANTLR4_LIBRARIES
    NAMES antlr4-runtime
    PATHS /usr/lib /usr/local/lib
)

if(NOT ANTLR4_INCLUDE_DIR OR NOT ANTLR4_LIBRARIES)
    message(FATAL_ERROR "ANTLR4 runtime not found. Please install libantlr4-runtime-dev")
endif()

message(STATUS "Found ANTLR4 include: ${ANTLR4_INCLUDE_DIR}")
message(STATUS "Found ANTLR4 library: ${ANTLR4_LIBRARIES}")

# Find LLVM
find_package(LLVM REQUIRED CONFIG)
message(STATUS "Found LLVM ${LLVM_PACKAGE_VERSION}")
message(STATUS "Using LLVMConfig.cmake in: ${LLVM_DIR}")

# Add LLVM definitions and include directories
add_definitions(${LLVM_DEFINITIONS})
include_directories(${LLVM_INCLUDE_DIRS})
llvm_map_components_to_libnames(llvm_libs
    core
    support
    analysis
    transformutils
    scalaropts
    instcombine
    ipo
    passes
)

# Add subdirectories
add_subdirectory(frontend)
add_subdirectory(backend)
add_subdirectory(src)

# Main executable
add_executable(scanner
    src/main.cpp
)

target_link_libraries(scanner
    frontend
    backend
    backend_coordinator
    ${ANTLR4_LIBRARIES}
    ${llvm_libs}
)

target_include_directories(scanner PRIVATE
    ${ANTLR4_INCLUDE_DIR}
    frontend/generated
    backend
    backend/ir_generation
    backend/optimization
    backend/codegen
)

